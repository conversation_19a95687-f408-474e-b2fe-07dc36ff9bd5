@import 'src/assets/sass/components/_variables.demo.scss';
$purple-Color: $primary;
#staffedpositionReport {
  .detail-sidebar {
    width: 500px;
    max-width: 100%;
  }

  .add-contact .card-body.create-card {
    box-shadow: none;
    margin: 0;
    padding: 20px !important;
  }

  .card-body {
    &.table {
      overflow: auto !important;
      scrollbar-width: none !important;
    }
  }

  // .form-check {
  //   bottom: 4px;
  //   position: absolute;
  // }

  .form-group {
    padding-bottom: 0;

    &:first-child {
      padding-top: 0;
    }
  }

  ::ng-deep .dropdown .p-dropdown,
  ::ng-deep .p-multiselect {
    width: 100%;
    height: 100%;
    border-radius: 9px !important;
    border: none !important;
    background-color: #f8f8ff !important;
    min-height: 30px !important;
  }

  ::ng-deep .dropdown .p-dropdown .p-dropdown-label,
  ::ng-deep .p-multiselect .p-multiselect-label {
    color: #000000;
    font-family: Pop<PERSON>s;
    font-size: 16px;
    font-weight: 500;
    letter-spacing: -0.32px;
    line-height: 25px;
    padding-top: 0.5rem;
    padding-left: 1rem;
  }

  ::ng-deep .dropdown .p-dropdown .p-dropdown-label.p-placeholder,
  ::ng-deep .p-multiselect .p-multiselect-label.p-placeholder {
    color: #b5b5c3 !important;
  }

  ::ng-deep .range-calender .p-calendar-w-btn {
    height: 100%;
    width: 100%;
  }

  ::ng-deep .range-calender .p-calendar-w-btn .p-inputtext {
    border: 0;
    background-color: #f8f8ff !important;
    height: 60px;
  }

  ::ng-deep .p-button.p-button-icon-only {
    width: 3.357rem;
  }

  ::ng-deep .p-inputtext:enabled:focus {
    box-shadow: none;
  }

  ::ng-deep .p-paginator .p-paginator-pages .p-paginator-page:not(.p-highlight):hover {
    border-radius: 50%;
  }

  ::ng-deep .p-paginator .p-paginator-first:not(.p-disabled):not(.p-highlight):hover,
  ::ng-deep .p-paginator .p-paginator-prev:not(.p-disabled):not(.p-highlight):hover,
  ::ng-deep .p-paginator .p-paginator-next:not(.p-disabled):not(.p-highlight):hover,
  ::ng-deep .p-paginator .p-paginator-last:not(.p-disabled):not(.p-highlight):hover {
    background: #e9ecef;
    border-color: transparent;
    color: #495057;
    border-radius: 50%;
  }

  ::ng-deep .p-paginator .p-paginator-first:not(.p-disabled):not(.p-highlight),
  ::ng-deep .p-paginator .p-paginator-prev:not(.p-disabled):not(.p-highlight),
  ::ng-deep .p-paginator .p-paginator-next:not(.p-disabled):not(.p-highlight),
  ::ng-deep .p-paginator .p-paginator-last:not(.p-disabled):not(.p-highlight) {
    border-radius: 50%;
    background-color: #f4f5f8;
  }

  ::ng-deep .p-paginator .p-paginator-pages .p-paginator-page {
    font-size: 14px;

    &.p-highlight {
      background: #4b3f72;
      border-color: #e3f2fd;
      color: #ffffff;
      border-radius: 50%;
    }
  }

  //TODO: uncemment if required
  // ::ng-deep .table .p-datatable {
  //   padding-bottom: 2rem;
  // }

  ::ng-deep .table .p-datatable .p-datatable-thead > tr > th {
    height: 36px;
    color: #4b3f72;
    font-family: Poppins;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 21px;
    border-bottom: none;
    border: 1px solid #edeff3;
    background-color: #ecedf6 !important;
    position: sticky;
    top: 0px;
    padding: 0.5rem 1rem;
  }

  .header-width-date {
    width: 12%;
  }

  .header-width-action {
    width: 3%;
    max-width: 3% !important;
    display: flex;
    flex-direction: column;
  }

  ::ng-deep .table .p-datatable .p-datatable-tbody > tr > td {
    color: #000000;
    font-family: Poppins;
    font-size: 12px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 15px;
    padding: 0.5rem 1rem;
    border: 1px solid #edeff3;
    height: 40px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  ::ng-deep .active {
    .p-datatable-wrapper {
      height: 100% !important;
    }
  }

  ::ng-deep .editing-row td {
    padding: 2.5rem 1rem !important;
    animation: fadeInAnimation ease 1.5s;
    animation-iteration-count: 1;
    animation-fill-mode: forwards;
  }

  ::ng-deep .pi-icon .pi {
    font-size: 0.5rem;
  }

  ::ng-deep .svg-icon-white .svg-icon svg g [fill] {
    fill: white !important;
  }

  ::ng-deep .p-datatable .p-sortable-column.p-highlight:hover .p-sortable-column-icon {
    color: #4b3f72;
  }

  ::ng-deep .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon {
    color: #4b3f72;
  }

  ::ng-deep .p-datatable .p-sortable-column.p-highlight,
  .p-datatable .p-sortable-column.p-highlight:hover {
    background: #f8f9fa;
    color: #4b3f72;
  }

  ::ng-deep .p-datatable .p-sortable-column.p-highlight:hover {
    background: #f8f9fa;
    color: #4b3f72;
  }

  ::ng-deep .p-datatable .p-sortable-column:focus {
    box-shadow: inset 0 0 0 0.2rem #4b3f72;
    outline: 0 none;
  }

  ::ng-deep .p-paginator .p-dropdown {
    width: 91px;
  }

  ::ng-deep .p-paginator {
    display: flex !important;
    justify-content: flex-start !important;
    border: none;
    margin-top: 15px;

    .p-paginator-current {
      position: absolute;
      right: 0;
      color: #575962;
      font-size: 14px;
      letter-spacing: 0;
      font-weight: 500;
      cursor: default;
    }

    .p-paginator-rpp-options {
      margin-left: 20px;
      height: 37px;
      width: 100px;
      border-radius: 20px;
      background-color: #f4f5f8;
      border: none;
    }
  }

  ::ng-deep .skill-set-filter .p-dropdown-clear-icon {
    margin-right: 15px !important;
  }

  @media (max-width: 500px) {
    ::ng-deep .p-paginator-current {
      bottom: 0;
    }

    ::ng-deep .p-paginator-rpp-options {
      margin-top: 10px;
    }
  }

  ::ng-deep .p-datatable .p-datatable-loading-overlay {
    top: 80px;
    background-color: transparent;
  }

  ::ng-deep .p-dropdown:not(.p-disabled).p-focus {
    box-shadow: none;
  }

  ::ng-deep .p-multiselect:not(.p-disabled).p-focus {
    box-shadow: none;
  }

  .center-align {
    text-align: center !important;
  }

  .help-icon {
    color: #b5b5c3;
    cursor: pointer;
  }

  .filter-listing {
    position: absolute;
    z-index: 10;
    box-sizing: border-box;
    width: 200px;
    border: 1px solid #efeff7;
    border-radius: 4px;
    background-color: #ffffff;
    box-shadow: 0 12px 30px 0 rgba(215, 215, 230, 0.4);
    overflow-y: auto;
    padding: 15px 15px;
  }

  .my-custom-class.heading {
    color: #9b9b9b;
    font-family: Poppins;
    font-size: 10px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 16px;
  }

  .my-custom-class.sub-heading {
    color: #242424;
    font-family: Poppins;
    font-size: 12px;
    letter-spacing: 0;
    line-height: 18px;
  }

  ::-webkit-scrollbar {
    width: 0.5em !important;
  }

  ::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3) !important;
  }

  ::-webkit-scrollbar-thumb {
    background-color: darkgray !important;
    outline: 1px solid slategray !important;
  }

  // ::ng-deep .p-datatable-flex-scrollable {
  //   height: calc(100% - 18px) !important;
  // }

  ::ng-deep .p-checkbox .p-checkbox-box.p-highlight {
    border-color: #827da0 !important;
    background: #827da0 !important;
  }

  ::ng-deep .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box:hover {
    border-color: #827da0;
  }

  ::ng-deep .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box .p-highlight:hover {
    border-color: #827da0 !important;
    background: #827da0 !important;
  }

  ::ng-deep .p-multiselect-panel .p-multiselect-items .p-multiselect-item.p-highlight {
    border-color: #dcdcdd;
    background: #dcdcdd;
  }

  ::ng-deep .p-multiselect-panel .p-multiselect-items .p-multiselect-item:focus {
    box-shadow: none;
  }

  ::ng-deep .p-multiselect.p-multiselect-chip .p-multiselect-token {
    background: #f4f6f8;
    border: 1px solid #edeff3;
  }

  .text-number-right {
    text-align: right !important;
  }

  // ::ng-deep .table .p-datatable .p-datatable-tbody > tr > td{
  //   text-align: right !important;
  //   display: block;
  // }
  // .scrollable-content {
  //   max-height: calc((var(--fixed-content-height, 1vh) * 100) - 100px) !important;
  //   overflow: hidden !important;
  // }

  // ::ng-deep .p-datatable-unfrozen-view .p-datatable-scrollable-body{
  //   overflow-y: auto !important;
  // }
  @media (min-width: 1024px) and (max-width: 1369px) {
    .fix-col-3 {
      width: 130px;
    }

    .dynamic-col-3 {
      width: 130px;
    }

    .dynamic-col-more {
      width: 130px;
    }
  }

  .show-pointer {
    cursor: pointer;
    color: #4b3f72 !important;
    text-decoration: underline;
  }
  @media (min-width: 1370px) and (max-width: 1700px) {
    .fix-col-3 {
      width: 150px;
    }

    .dynamic-col-3 {
      width: 155px;
    }

    .dynamic-col-more {
      width: 120px;
    }
  }

  @media (min-width: 1700px) and (max-width: 1800px) {
    .fix-col-3 {
      width: 170px;
    }

    .dynamic-col-3 {
      width: 180px;
    }

    .dynamic-col-more {
      width: 120px;
    }
  }

  @media (min-width: 1800px) and (max-width: 2000px) {
    .fix-col-3 {
      width: 185px;
    }

    .dynamic-col-3 {
      width: 190px;
    }

    .dynamic-col-more {
      width: 120px;
    }
  }

  @media (min-width: 2000px) {
    .fix-col-3 {
      width: 210px;
    }

    .dynamic-col-3 {
      width: 215px;
    }

    .dynamic-col-more {
      width: 120px;
    }
  }

  .background {
    height: 38px;
    border-radius: 9px 9px 0 0;
    background-color: #eeebf4;
    display: flex;
    align-items: center;
    padding-left: 0.5rem;
  }

  .PL-border {
    box-sizing: border-box;
    border: 1px solid #eeebf4;
    border-radius: 9px;
    margin-bottom: 1rem;
  }

  input[type='radio'] {
    display: none;
  }

  input[type='radio']:checked + label:before {
    background: #4b3f72;
    color: #ffffff;
    content: '\2713';
    text-align: center;
  }

  input[type='radio'] + label:before {
    border: 1px solid #4b3f72;
    border-radius: 1rem;
    content: '\00a0';
    display: inline-block;
    font: 16px/1em sans-serif;
    height: 16px;
    // margin: 0.2em 0.25em 0 0;
    padding: 0;
    vertical-align: top;
    width: 16px;
    margin-top: 1px !important;
  }

  .save-filter-radio {
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }

  .width-65 {
    width: 65%;
  }

  ::ng-deep {
    .right-align {
      display: flex;
      justify-content: flex-end;
    }

    .pi-chevron-down:before {
      color: #000;
      font-size: 16px;
      font-weight: 500;
    }
  }
}

::ng-deep .confirm-dialog .p-dialog {
  width: 30vw;
}

::ng-deep .filter-dialog {
  .p-component-overlay {
    background: none !important;
    animation: none !important;
  }

  .p-dialog {
    height: 253px;
    width: 400px;
    top: 51px;

    .p-dialog-header {
      display: none;
    }

    .p-dialog-content {
      padding-top: 1rem;
    }

    .title {
      color: #757575;
      font-family: Poppins;
      font-size: 12px;
      letter-spacing: 0;
      line-height: 18px;
    }

    .share-icon {
      background-color: #4b3f72 !important;
      border-color: #4b3f72 !important;

      i,
      em {
        color: white;
      }
    }

    .filter-body {
      display: flex;
      align-content: center;
      align-items: center;
    }

    .form-check {
      height: 40px;
      background-color: white;
      display: flex;
      align-items: center;
      width: 100%;

      .form-check-label,
      .form-check-label:hover {
        cursor: pointer;
        color: black;
        width: 75%;
        color: #000000;
        font-family: Poppins;
        font-size: 12px;
        letter-spacing: 0;
        line-height: 18px;
      }

      .div {
        width: 30%;
        display: flex;
        justify-content: flex-end;
      }
    }

    .filter-icons {
      display: flex;
      width: 100%;
      justify-content: flex-end;
    }
  }
}

.form-group {
  padding-bottom: 0;

  &:first-child {
    padding-top: 0;
  }
}

::ng-deep .dropdown .p-dropdown,
::ng-deep .dropdown .p-dropdown .p-focus {
  width: 100%;
  height: 100%;
  border-radius: 9px !important;
  border: none !important;
  box-shadow: none !important;
  background-color: #f8f8ff !important;
  min-height: 60px !important;
  padding: 1.2rem;
}

::ng-deep .dropdown .p-dropdown .p-dropdown-label {
  color: #000000;
  font-family: Poppins;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: -0.32px;
  line-height: 25px;
}

::ng-deep .dropdown .p-dropdown .p-dropdown-label.p-placeholder {
  color: #b5b5c3 !important;
}

.editRow {
  border: 1px solid #ced4da !important;
  border-radius: 6px;
}

.position-abs {
  position: absolute;
}

.p-inputtext {
  width: 100%;
}

p-celleditor.p-element {
  width: 100%;
}

::ng-deep .p-datatable .p-datatable-header {
  padding: 0 1rem;
}

.action-buttons {
  justify-content: center;
}

::ng-deep .p-inputtext:enabled:focus {
  box-shadow: none !important;
}

::ng-deep .p-button.p-button-success:enabled:focus,
.p-button.p-button-danger:enabled:focus {
  box-shadow: none !important;
}

::ng-deep .p-button.p-button-success.p-button-text:enabled:active,
.p-button.p-button-danger.p-button-text:enabled:active {
  background: transparent;
}

::ng-deep .p-dropdown:not(.p-disabled):hover,
::ng-deep .p-dropdown:not(.p-disabled).p-focus {
  border: 1px solid #ced4da;
  border-radius: 6px;
  background-color: #ffffff;
  box-shadow: 0 0 2px 1px rgba(17, 157, 164, 0.2);
}

::ng-deep .p-inputtext:enabled:focus,
::ng-deep .p-inputtext:enabled:hover {
  border: 1px solid #ced4da !important;
  border-radius: 6px;
  background-color: #ffffff;
  box-shadow: 0 0 2px 1px rgba(17, 157, 164, 0.2);
}

::ng-deep .p-calendar .p-inputtext {
  height: 30px !important;
  width: 100% !important;
  border: 1px solid #b5b5c3;
  border-radius: 6px;
  background-color: #ffffff;
}

.danger-control ::ng-deep .p-inputtext {
  border: 1px solid red;
}

::ng-deep .editing-row {
  box-shadow: inset 0px 0px 5px 0px #4b3f72 !important;
}

@keyframes fadeInAnimation {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

::ng-deep .p-treeselect {
  width: 100%;
}

::ng-deep .dialog-applied-tags {
  .p-dialog {
    width: 460px;
  }
}

::ng-deep .p-dialog-content li {
  font-size: 16px;
}

.client-width,
.project-width,
.position-width,
.tags-width {
  width: 20% !important;
}

.employee-width {
  width: 18% !important;
}

.monthly-data-width {
  width: 10% !important;
}

.status-width {
  width: 9.5% !important;
}

.billrate-width {
  width: 10.5% !important;
}

.skill-width {
  width: 8.5% !important;
}

.start-end-date {
  width: 13% !important;
}

.hours-width {
  min-width: 150px;
  width: 8.5% !important;
}

.extended-field-header {
  min-width: 120px;
}

.calendar-filter-panel {
  display: flex;
  justify-content: space-between;
}

.calendar-filter-body {
  display: flex;
  // justify-content: space-between;
  align-items: center;
}

.calendar-cust-btn {
  color: #4b3f72 !important;
}

.calendar-cust-btn:hover {
  color: #fff !important;
}

.calendar-cust-save-btn {
  margin-right: 15px;
  color: #fff !important;
}

.calendar-view-wrapper {
  overflow: auto !important;
  scrollbar-width: none !important;
}

// popup component
.md-tooltip .mbsc-popup-content {
  padding: 0;
}

.md-tooltip {
  font-size: 15px;
  font-weight: 600;
}

.md-tooltip-header {
  padding: 12px 16px;
  color: #eee;
}

.md-tooltip-info {
  padding: 16px 16px 10px 16px;
  position: relative;
  line-height: 32px;
}

.md-tooltip-time,
.md-tooltip-status-button {
  float: right;
}

.md-tooltip-title {
  margin-bottom: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.md-tooltip-text {
  font-weight: 300;
}

.md-tooltip-info .mbsc-button {
  font-size: 14px;
  margin: 0;
}

.md-tooltip-info .mbsc-button.mbsc-material {
  font-size: 12px;
}

.md-tooltip-view-button {
  position: absolute;
  bottom: 16px;
  left: 16px;
}

.md-tooltip-delete-button {
  position: absolute;
  bottom: 16px;
  right: 16px;
}

::ng-deep {
  .week-month-popup-wrapper {
    .mbsc-popup-anchored {
      margin-left: 78rem;
      left: auto !important;
    }
  }
}

// popup component
// calendar bars
.md-timeline-template-event {
  border-radius: 8px;
}

::ng-deep .md-timeline-template-event-cont {
  display: flex;
  justify-content: center;
  flex-direction: column;
}

// .md-timeline-template-event-cont {
//   display: flex;
//   justify-content: center;
//   flex-direction: column;
// }

// ::ng-deep .mbsc-has-sticky .mbsc-timeline-resources.mbsc-ltr {
//   width: auto !important;
// }

// custom header
.md-resource-header-template .mbsc-timeline-resource-col {
  width: 230px;
}

.md-resource-header-template .mbsc-timeline-resource-header,
.md-resource-header-template .mbsc-timeline-resource-title {
  padding: 0;
}

.md-resource-header-template .mbsc-timeline-resource-title {
  height: 100%;
}

.md-resource-header-template-cont {
  line-height: 50px;
  height: 100%;
}

.md-resource-header-template-name {
  height: 100%;
  display: block;
  padding: 0 5px;
  float: right;
  width: 50%;
  line-height: 14px;
  border-left: 1px solid #ccc;
}

.md-resource-header-template-seats {
  display: block;
  width: 50%;
  height: 100%;
  float: left;
  line-height: 14px;
  padding: 0 5px;
}

.md-resource-header-template-title {
  font-weight: 600;
  line-height: 56px;
}

.add-ellipsis {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

@supports (overflow: clip) {
  .md-resource-header-template.mbsc-ltr .mbsc-schedule-event-inner {
    left: 230px;
  }

  .md-resource-header-template.mbsc-rtl .mbsc-schedule-event-inner {
    right: 230px;
  }
}

// custom header

:host ::ng-deep .custom-spinner .p-progress-spinner-circle {
  animation: custom 8s ease-in-out infinite;
}

:host ::ng-deep .custom-spinner {
  position: absolute;
  top: 50vh;
  z-index: 99;
  left: 50%;
}

@keyframes custom {
  100%,
  0% {
    stroke: #b5b5c3;
  }

  40% {
    stroke: #b5b5c3;
  }

  66% {
    stroke: #b5b5c3;
  }

  80%,
  90% {
    stroke: #b5b5c3;
  }
}

::ng-deep {
  .mbsc-timeline-resource-header,
  .mbsc-timeline-header {
    font-family: Poppins;
    font-size: 14px;
    font-weight: 500;
    color: #4b3f72;
  }
  .mbsc-flex-none {
    margin-left: 12px;
  }
}

.f-s-20 {
  font-size: 20px !important;
}

.card-header.custom {
  align-items: center !important;

  .card-title {
    display: block !important;

    p {
      font-size: 13px;
      margin-bottom: 0;
    }
  }
}

::ng-deep .export-btn .p-splitbutton {
  min-width: 80px;
  height: 36px;
  display: flex;
  justify-content: center;
}

::ng-deep .export-btn .p-splitbutton .p-splitbutton-defaultbutton {
  border-top-left-radius: 6px !important;
  border-bottom-left-radius: 6px !important;
}

::ng-deep .export-btn .p-button {
  border-radius: 6px;
  border-top-left-radius: 1px;
  border-bottom-left-radius: 1px;
}

::ng-deep .export-btn .p-button:focus {
  box-shadow: none;
}

::ng-deep .export-btn .p-menu {
  width: 7.5rem;
}

::ng-deep .export-btn .p-menu .p-menuitem-link .p-menuitem-icon {
  font-size: 1.5rem;
  color: #4b3f72;
}

::ng-deep .export-btn .p-menu .p-menuitem-link .p-menuitem-text {
  color: #242424;
  font-family: Poppins;
  font-size: 13px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 20px;
}

::ng-deep .export-btn .p-button-label {
  color: #ffffff;
  font-family: Poppins;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 21px;
  text-align: center;
}

::ng-deep .btn-filter-icon {
  background: #4b3f72;
  border-radius: 6px;
  margin-right: 1rem;
}

::ng-deep .p-checkbox .p-checkbox-box .p-checkbox-icon {
  transition-duration: 0.2s;
  color: black;
  background-color: #8975a1;
}

::ng-deep .p-multiselect-panel .p-multiselect-items .p-multiselect-item:not(.p-highlight):not(.p-disabled):hover {
  background: #f4f6f8 !important;
  border: 1px solid #edeff3 !important;
}

::ng-deep .p-multiselect-items-wrapper {
  .p-highlight {
    background: #f4f6f8 !important;
    color: #000;
    border: 1px solid #edeff3 !important;
  }
}
@media (max-width: 500px) {
  .card-header.custom .card-title {
    margin: 10px 0 !important;
  }

  .card-label {
    font-weight: 800 !important;
    font-size: 12px !important;
  }
}

::ng-deep .p-button {
  background: #4b3f72;
  border-color: #4b3f72;
}

::ng-deep .p-button:enabled:hover {
  background: #574985;
  border-color: #574985;
}

.btn-switcher {
  border-color: transparent;
}

.btn-switcher.btn-right {
  border-radius: 0 6px 6px 0;

  svg {
    background: #574985;
  }
}

.btn-switcher.btn-left {
  border-radius: 6px 0 0 6px;

  svg {
    background: #574985;
  }
}

.btn-switcher.switch-active {
  background: #574985;

  svg {
    background: #fff;
  }
}

.w-50 {
  width: 50%;
}

::ng-deep .download svg {
  width: 26px !important;
  height: 26px !important;
}

.export-action-listing {
  display: flex;
  align-items: center;
  flex-direction: column;
  button {
    width: 100%;
    background-color: #f1f1f1;
    color: #000;
    &:hover {
      background-color: #b9b9b9;
      color: #000;
    }
    ::ng-deep .p-button-icon {
      font-size: 1.2rem;
      margin-left: 1rem;
    }
  }
}

::ng-deep .export-dialog {
  .p-dialog {
    height: 128px;
    width: 170px;
    .p-dialog-header {
      display: none;
    }
  }
  .p-dialog .p-dialog-content {
    padding: 0 5px;
  }
}

.selected-columns-switcher {
  display: flex;
  justify-content: space-between;
}

::ng-deep {
  span.tag-count p-badge span {
    background-color: #4b3f72;
  }
  .p-datatable-table {
    position: relative;
  }
}

.tooltip-hover {
  position: relative;
}

.tags-td {
  display: flex;
  justify-content: space-between;
}

.switcher-flex-end {
  justify-content: flex-end;
}

.multiselect-wrapper {
  margin-top: 13px;
}

.apply-filter-msg {
  font-size: 14px;
  font-weight: 500;
  background-color: #ffffff;
}

::ng-deep .mbsc-eventcalendar {
  height: 89%;
}

::ng-deep .mbsc-timeline-header-week-last.mbsc-ltr {
  min-width: 144px;
  height: 23px;
}

.mbsc-timeline-header-week-last.mbsc-ltr {
  min-width: 144px;
}

::ng-deep .mbsc-timeline-column.mbsc-ltr {
  margin: 0px !important;
}

::ng-deep .mbsc-timeline-header-bg > div:nth-child(2) {
  margin-left: 0px;
}

::ng-deep .mbsc-timeline-header-bg > div:nth-child(3) {
  margin-left: 0px;
}

::ng-deep .mbsc-timeline-header .mbsc-timeline-resource-header-cont {
  width: 192px;
}

::ng-deep .mbsc-flex-none {
  margin-left: 0px !important;
}

::ng-deep .popup-column {
  z-index: 100 !important;
  position: absolute !important;
  right: 5px !important;
  top: 105px !important;
}

#staffedpositionReport {
  ::ng-deep .mat-drawer.mat-drawer-end {
    transform: none !important;
  }

  .pointer-disable {
    pointer-events: none !important;
  }

  .calender-hight {
    height: calc(100vh - 195px) !important;
    max-height: fit-content;
  }

  ::ng-deep .calender-hight-table {
    height: calc(100vh - 350px) !important;
  }

  ::ng-deep .p-datatable-customers .p-datatable-wrapper {
    height: auto !important;
    max-height: calc(100vh - 250px) !important;
  }

  --fixed-content-height: 6.5px;
}

::ng-deep .p-tooltip {
  .p-tooltip-text {
    background: $purple-Color !important;
    color: $primary-inverse !important;
    padding: 0.75rem 1rem !important;
    box-shadow: 0 2px 4px -1px $box-shadow !important;
    border-radius: 6px !important;
    font: 500 14px/1.4 inherit !important;
    text-align: left !important;
    white-space: pre-line !important;
    max-width: 300px !important;
  }

  .p-tooltip-arrow,
  &.p-tooltip-top .p-tooltip-arrow,
  &.p-tooltip-bottom .p-tooltip-arrow,
  &.p-tooltip-left .p-tooltip-arrow,
  &.p-tooltip-right .p-tooltip-arrow {
    border-color: transparent $purple-Color $purple-Color $purple-Color !important;
  }
}
