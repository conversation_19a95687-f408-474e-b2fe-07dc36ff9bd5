<div class="card card-custom gutter-b" id="create_role_form">
  <app-card-header [cardTitle]="cardTitle" [cardSubTitle]="cardSubTitle" [buttons]="buttons"></app-card-header>
  <div class="card-body create-card">
    <form class="form" autocomplete="off" [formGroup]="createRoleForm" autocomplete="off" novalidate="novalidate" (ngSubmit)="createRoleForm.valid && onSave()">
      <div id="client-detail">
        <kt-auth-notice></kt-auth-notice>
        <div class="mx-auto content-center">
          <div class="row">
            <div class="col-6 pr-md-5">
              <div class="form-group first">
                <label class="form-label">Role Name</label>
                <ng-container *ngIf="createRoleForm.get('name')?.value === appConstants.defaultRole || blockUpdateDelete; else editableName">
                  <p>
                    <label class="form-label">{{ createRoleForm?.get('name').value }}</label>
                  </p>
                </ng-container>
                <ng-template #editableName>
                  <input type="text" class="form-control custom" placeholder="e.g. Super User" formControlName="name" />
                  <app-form-error [validation]="'required'" [form]="createRoleForm" [controlName]="'name'" [fieldLabel]="'Role Name'"></app-form-error>
                </ng-template>
              </div>
            </div>
            <div class="col-6 pr-md-5" *ngIf="createRoleForm?.get('name')?.value === appConstants.defaultRole">
              <div class="form-group first">
                <label class="form-label">Default Role</label>
                <mat-slide-toggle class="mx-3" color="primary" formControlName="isDefault"></mat-slide-toggle>
                <app-form-error [validation]="'required'" [form]="createRoleForm" [controlName]="'isDefault'" [fieldLabel]="'Is Default'"></app-form-error>
              </div>
            </div>
            <div class="col-12 pr-md-5">
              <div class="form-group first">
                <label class="form-label">Description</label>
                <ng-container *ngIf="createRoleForm?.get('name')?.value === appConstants.defaultRole; else editableDescription">
                  <p>
                    <label class="form-label">{{ createRoleForm?.get('description').value }}</label>
                  </p>
                </ng-container>
                <ng-template #editableDescription>
                  <textarea type="text" class="form-control custom" placeholder="Type description here" formControlName="description"></textarea>
                  <app-form-error [validation]="'required'" [form]="createRoleForm" [controlName]="'description'" [fieldLabel]="'Description'"></app-form-error>
                </ng-template>
              </div>
              <div class="form-group">
                <label _ class="form-label">Default Landing Page</label>
                <p-dropdown
                  [showClear]="true"
                  formControlName="navigation"
                  class="p-element p-inputwrapper form-control custom-dropdown"
                  [options]="groupedItems"
                  [group]="true"
                  optionLabel="title"
                  optionGroupLabel="group"
                  (onChange)="checkSelectedNavigationHasPermission()"
                  [filter]="true"
                  [placeholder]="'Select an Navigation'"
                  [required]="true"
                >
                  <ng-template let-group pTemplate="group">
                    <div class="p-dropdown-header">{{ group?.group }}</div>
                  </ng-template>
                </p-dropdown>
                <div>
                  <app-form-error [validation]="'required'" [form]="createRoleForm" [controlName]="'navigation'" [fieldLabel]="'Default Landing Page'"></app-form-error>
                  <small *ngIf="showNavigationErrorMessage && createRoleForm.get('navigation').value" class="text-danger">Please Add Permission for Selected Navigation</small>
                </div>
              </div>
              <div class="form-group">
                <label class="form-label">Screen Permissions</label>
                <p-table #dt [value]="rolesConst" dataKey="id" [rows]="10" styleClass="p-datatable-customers" [filterDelay]="0" [sortField]="'name'" [sortOrder]="-1">
                  <ng-template pTemplate="header">
                    <tr>
                      <th id="role" class="header-width-extra">Features</th>
                      <th id="description" class="header-width align-center">No Access</th>
                      <th id="description" class="header-width align-center">View Access</th>
                      <th id="description" class="header-width align-center">Full Access</th>
                    </tr>
                  </ng-template>
                  <ng-template pTemplate="body" let-role let-i="rowIndex">
                    <tr [ngClass]="i % 2 === 0 ? 'even-row' : 'odd-row'">
                      <td class="font-weight-bolder" style="width: 45%">
                        {{ role?.feature }}
                        <button
                          type="button"
                          pButton
                          pRipple
                          class="p-button-text p-button-rounded p-button-plain"
                          style="vertical-align: middle"
                          [icon]="role?.expand ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"
                          (click)="toggleButton(role)"
                        ></button>
                      </td>
                      <td class="align-center radio-btn">
                        <!-- <input type="radio" [value]="permissionsEnum.NO_ACCESS + role.feature" [name]="role.feature" [checked]="role?.allFeatures?.includes(permissionsEnum.NO_ACCESS)"
                        (change)="AllPermissionCheck($event,role)"> -->
                        <!-- <label class="container1">
                          <input type="radio" [value]="permissionsEnum.NO_ACCESS + role.feature" [name]="role.feature" [checked]="role?.allFeatures?.includes(permissionsEnum.NO_ACCESS)"
                          (change)="AllPermissionCheck($event,role)">
                          <span class="checkmark"></span>
                        </label> -->
                      </td>
                      <td class="align-center radio-btn">
                        <!-- <input type="radio" [value]="permissionsEnum.CAN_VIEW + role.feature" [name]="role.feature" [checked]="role?.allFeatures?.includes(permissionsEnum.CAN_VIEW)"
                        (change)="AllPermissionCheck($event,role)"> -->
                        <!-- <label class="container1">
                        <input type="radio" [value]="permissionsEnum.CAN_VIEW + role.feature" [name]="role.feature" [checked]="role?.allFeatures?.includes(permissionsEnum.CAN_VIEW)"
                          (change)="AllPermissionCheck($event,role)">
                          <span class="checkmark"></span>
                        </label> -->
                      </td>
                      <td class="align-center radio-btn">
                        <!-- <input type="radio" [value]="permissionsEnum.CAN_MANAGE + role.feature" [name]="role.feature" [checked]="role?.allFeatures?.includes(permissionsEnum.CAN_MANAGE)"
                        (change)="AllPermissionCheck($event,role,true)"> -->
                        <!-- <label class="container1">
                        <input type="radio" [value]="permissionsEnum.CAN_MANAGE + role.feature" [name]="role.feature" [checked]="role?.allFeatures?.includes(permissionsEnum.CAN_MANAGE)"
                          (change)="AllPermissionCheck($event,role,true)">
                          <span class="checkmark"></span>
                        </label> -->
                      </td>
                    </tr>
                    <ng-container *ngIf="role.expand">
                      <ng-container *ngFor="let sub of role.subfeature">
                        <ng-container *ngIf="sub?.feature; else noFeature">
                          <tr [ngClass]="i % 2 === 0 ? 'even-row' : 'odd-row'">
                            <td class="lvl-1 ml-8" style="display: inline">
                              <span class="font-weight-bolder">
                                {{ sub?.feature }}
                              </span>
                              <button
                                type="button"
                                pButton
                                pRipple
                                class="p-button-text p-button-rounded p-button-plain"
                                style="vertical-align: middle"
                                [icon]="sub?.expand ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"
                                (click)="toggleButton(sub)"
                              ></button>
                            </td>
                            <td class="align-center">
                              <input
                                type="radio"
                                [value]="permissionsEnum.NO_ACCESS + sub.module"
                                [name]="sub.name + sub.id"
                                [checked]="checkNoAccessPermission(sub?.permission, sub?.module)"
                                (click)="permissionCheckProject($event, role, sub, sub)"
                                *ngIf="sub?.includePermissions?.includes(permissionsEnum.NO_ACCESS)"
                              />
                            </td>
                            <td class="align-center">
                              <input
                                type="radio"
                                [value]="permissionsEnum.CAN_VIEW + sub.module"
                                [name]="sub.name + sub.id"
                                [checked]="sub?.permission?.includes(permissionsEnum.CAN_VIEW + sub.module)"
                                (click)="permissionCheckProject($event, role, sub, sub)"
                                *ngIf="sub?.includePermissions?.includes(permissionsEnum.CAN_VIEW)"
                              />
                            </td>
                            <td class="align-center">
                              <input
                                type="radio"
                                [value]="permissionsEnum.CAN_MANAGE + sub.module"
                                [name]="sub.name + sub.id"
                                [checked]="sub?.permission?.includes(permissionsEnum.CAN_MANAGE + sub.module)"
                                (click)="permissionCheckProject($event, role, sub, sub, true)"
                                *ngIf="sub?.includePermissions?.includes(permissionsEnum.CAN_MANAGE)"
                              />
                            </td>
                          </tr>
                          <ng-container *ngIf="sub.expand">
                            <tr *ngFor="let subfeat of sub.subfeature" [ngClass]="i % 2 === 0 ? 'even-row' : 'odd-row'">
                              <ng-container *ngIf="!subfeat.isHide">
                                <td class="font-weight-bolder ml-15" style="display: inline">
                                  {{ subfeat?.name }}
                                </td>
                                <td class="align-center">
                                  <input
                                    type="radio"
                                    [value]="permissionsEnum.NO_ACCESS + subfeat.module"
                                    [name]="subfeat.name + subfeat.id"
                                    [checked]="checkNoAccessPermission(subfeat?.permission, subfeat?.module)"
                                    (click)="permissionCheckProject($event, role, sub, subfeat)"
                                    *ngIf="subfeat?.includePermissions?.includes(permissionsEnum.NO_ACCESS)"
                                  />
                                </td>
                                <td class="align-center">
                                  <input
                                    type="radio"
                                    [value]="permissionsEnum.CAN_VIEW + subfeat.module"
                                    [name]="subfeat.name + subfeat.id"
                                    [checked]="subfeat?.permission?.includes(permissionsEnum.CAN_VIEW + subfeat.module)"
                                    (click)="permissionCheckProject($event, role, sub, subfeat)"
                                    *ngIf="subfeat?.includePermissions?.includes(permissionsEnum.CAN_VIEW)"
                                  />
                                </td>
                                <td class="align-center">
                                  <input
                                    type="radio"
                                    [value]="permissionsEnum.CAN_MANAGE + subfeat.module"
                                    [name]="subfeat.name + subfeat.id"
                                    [checked]="subfeat?.permission?.includes(permissionsEnum.CAN_MANAGE + subfeat.module)"
                                    (click)="permissionCheckProject($event, role, sub, subfeat, true)"
                                    *ngIf="subfeat?.includePermissions?.includes(permissionsEnum.CAN_MANAGE)"
                                  />
                                </td>
                              </ng-container>
                            </tr>
                          </ng-container>
                        </ng-container>
                        <ng-template #noFeature>
                          <tr [ngClass]="i % 2 === 0 ? 'even-row' : 'odd-row'">
                            <td class="font-weight-bolder ml-8" style="display: inline">
                              {{ sub?.name }}
                            </td>
                            <td class="align-center">
                              <input
                                type="radio"
                                [value]="permissionsEnum.NO_ACCESS + sub.module"
                                [name]="sub.name"
                                [checked]="checkNoAccessPermission(sub?.permission, sub?.module)"
                                (click)="permissionCheck($event, role, sub)"
                                *ngIf="checkInitialPermission(sub?.includePermissions, permissionsEnum.NO_ACCESS)"
                              />
                            </td>
                            <td class="align-center">
                              <input
                                type="radio"
                                [value]="permissionsEnum.CAN_VIEW + sub.module"
                                [name]="sub.name"
                                [checked]="sub?.permission?.includes(permissionsEnum.CAN_VIEW + sub.module)"
                                (click)="permissionCheck($event, role, sub)"
                                *ngIf="checkInitialPermission(sub?.includePermissions, permissionsEnum.CAN_VIEW)"
                              />
                            </td>
                            <td class="align-center">
                              <input
                                type="radio"
                                [value]="permissionsEnum.CAN_MANAGE + sub.module"
                                [name]="sub.name"
                                [checked]="sub?.permission?.includes(permissionsEnum.CAN_MANAGE + sub.module)"
                                (click)="permissionCheck($event, role, sub, true)"
                                *ngIf="checkInitialPermission(sub?.includePermissions, permissionsEnum.CAN_MANAGE)"
                              />
                            </td>
                          </tr>
                        </ng-template>
                      </ng-container>
                    </ng-container>
                  </ng-template>
                  <ng-template pTemplate="emptymessage">
                    <tr>
                      <td colspan="6" class="center-align">No Roles found.</td>
                    </tr>
                  </ng-template>
                </p-table>

                <!-- <br>
                <label class="form-label">Other Permissions</label>
                <br>
                <div class="checkbox-wrapper">
                  <input type="checkbox" [checked]="projectStatus" id="terms" (change)="changeProjectStatus($event)" class="checkbox">
                  <label class="checkbox-label font-weight-bolder" for="terms">Change Project Status </label>
                </div> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
