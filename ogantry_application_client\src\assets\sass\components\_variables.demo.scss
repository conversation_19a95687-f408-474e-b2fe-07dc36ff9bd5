//
// To make future updates easier consider overriding the global variables from _variables.bootstrap.scss and _variables.custom.scss for current demo in this file.
// Note that this file is included first and variables defined in _variables.bootstrap.scss and _variables.custom.scss
// are not accessible in this file but you can override any global variable as shown below:
//

// Theme colors
// Override primary color variants
$primary: #4b3f72; // Bootstrap variable
$primary-hover: #574985; // Custom variable
$primary-light: #e1f0ff; // Custom variable
$primary-inverse: #ffffff; // Custom variable

// Additional colors from project-setup component
$black: #000000;
$light-gray: #b5b5c3;
$medium-gray: #8f8f9b;
$light-bg: #f8f8ff;
$light-purple-bg: #eeeef6;
$light-purple-bg-2: #ecedf6;
$calendar-border-color: #e4e6ef;
$chip-hover-bg: #d0e7ff;
$disabled-gray: #d3d3d3;

// Alert colors
$danger: #f44336;
$danger-light-bg: #fff5f5;
$danger-light-border: #ffcccc;
$danger-dark: #cc0000;
$info-light-bg: #f0f7ff;
$info-light-border: #cce5ff;
$info-dark: #004085;
$box-shadow: rgb(0 0 0 / 20%), 0 4px 5px 0 rgb(0 0 0 / 14%), 0 1px 10px 0 rgb(0 0 0 / 12%);

// Input hover effect colors
$input-hover-border: #119da4;
$input-hover-bg: #ffffff;
$input-hover-shadow: 0 0 2px 1px rgba(17, 157, 164, 0.2);
$input-default-border: #ced4da;
$tr-hover-background: #e9ecef;
$tr-hover-color-text: #495057;
