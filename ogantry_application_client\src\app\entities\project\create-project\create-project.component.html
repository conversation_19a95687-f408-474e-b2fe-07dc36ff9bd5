<span class="create-project-wrapper">
  <mat-drawer-container class="h-100" id="benchReportContainer">
    <mat-drawer class="w-50 detail-sidebar" #sidebarEdit mode="over" position="end" disableClose [opened]="openFilter">
      <app-add-contact
        *ngIf="isAddContactVisible"
        (closeSidebarEvent)="sidebarClosed($event)"
        [sidebarParams]="sidebarParams"
        [selectedContact]="selectedContact"
        (updateTheClientList)="updateClientList($event)"
        [project]="project"
      ></app-add-contact>
      <app-new-client *ngIf="isAddNewClient" (closeSidebarEvent)="sidebarClosed($event)" (updateTheCustomerList)="updateCustomerList($event)"></app-new-client>
      <app-add-edit-purchase-order
        *ngIf="purchaseOrderSidebarVisible && clientId"
        [clientId]="clientId"
        [isEdit]="false"
        (closeSidebarEvent)="closePurchaseOrderSidebar()"
        (updatePurchaseOrderList)="listPurchaseOrder(this.clientId)"
      ></app-add-edit-purchase-order>
      <!-- <app-extended-form
        *ngIf="isExtendFiled"
        [projectId]="projectId"
        [componentType]="componentType.Project"
        (closeSidebarEvent)="sidebarClosed($event)"
        (dataChangeEvent)="extendFieldsDataChange()"
      ></app-extended-form> -->
    </mat-drawer>
    <mat-drawer-container class="detail-sidebar-content">
      <div class="card card-custom" id="createProject">
        <div class="card-body pt-0">
          <!--begin: Wizard -->
          <div #wizard class="wizard wizard-4" id="kt_wizard_v4" data-wizard-state="step-first">
            <!--begin: Form Wizard Nav -->
            <div class="wizard-nav">
              <div class="wizard-steps">
                <a class="wizard-step" data-wizard-type="step" data-wizard-state="current" data-wizard-clickable="true" (click)="goToNextStep('1')">
                  <div class="wizard-wrapper">
                    <div class="wizard-number">1</div>
                    <div class="wizard-label">
                      <div class="wizard-title">Project Setup</div>
                    </div>
                  </div>
                </a>
                <a class="wizard-step" data-wizard-type="step" data-wizard-clickable="true" (click)="goToNextStep('2')">
                  <div class="wizard-wrapper">
                    <div class="wizard-number">2</div>
                    <div class="wizard-label">
                      <div class="wizard-title">Position Setup</div>
                    </div>
                  </div>
                </a>
                <a class="wizard-step" data-wizard-type="step" data-wizard-clickable="true" (click)="goToNextStep('3')">
                  <div class="wizard-wrapper">
                    <div class="wizard-number">3</div>
                    <div class="wizard-label">
                      <div class="wizard-title">Financial Review</div>
                    </div>
                  </div>
                </a>
              </div>
            </div>
            <!--end: Form Wizard Nav -->
            <div *ngIf="projectId" class="fixed-content pl-4 pt-4">
              <div class="d-inline-block font-weight-bold">Client Name : {{ overAllProjectData?.project?.customer?.name }}</div>
              <div class="d-inline-block font-weight-bold ml-8">Project Name : {{ overAllProjectData?.project?.name }}</div>
              <div class="d-inline-block font-weight-bold ml-8 start-date">
                Duration :
                {{ overAllProjectData?.project?.start_date | date : 'MM/dd/yyyy' }}
                to
                {{ overAllProjectData?.project?.end_date | date : 'MM/dd/yyyy' }}
              </div>
            </div>
            <app-project-cost
              [projectCost]="projectionData?.total"
              [loading]="projectCostDataLoading"
              [recalculating]="recalculating"
              [projectStatusLabel]="projectStateLabel"
              [currentProjectState]="currentProjectState"
              (resumeServices)="resumeValidation()"
              [projectId]="projectId"
            ></app-project-cost>
            <div class="card card-custom card-shadowless rounded-top-0 cal-height">
              <div class="card-body p-0">
                <div class="row justify-content-center full-height">
                  <div class="col-xl-12">
                    <!--begin: Form Wizard Form-->
                    <app-project-setup
                      [minDate]="startDate"
                      [maxDate]="endDate"
                      (checkValidationStatus)="checkValidationStatus()"
                      [hasPositionFlag]="hasAnyPosition"
                      (positionSetupTab)="goToNextTab()"
                      [_tags]="tags"
                      (extendSidebarOpen)="openSidebarExtendFiled($event)"
                      (callProjections)="callProjectionApi(true)"
                      (clientSidebarOpen)="openClientSidebar({ template: sidebarEdit }, $event)"
                      (sidebarOpen)="openSidebar({ template: sidebarEdit }, $event)"
                      (getProjectionData)="getProjectionData($event)"
                      [dailyExpenseTypes]="dailyExpenseTypes"
                      [monthlyExpenseTypes]="monthlyExpenseTypes"
                      [projectData]="project"
                      [isEdit]="isEdit"
                      (setProjectId)="setProjectId($event)"
                      [projectId]="projectId"
                      (overallProjectData)="setProjectData($event)"
                      [extendFieldsObj]="extendFieldsObj"
                      (secondStep)="goToNextStep('2')"
                    ></app-project-setup>
                    <!--end: Form Wizard Step 1-->

                    <!--begin: Form Wizard Step 2-->
                    <app-position-setup
                      #postionSetup
                      (checkValidationStatus)="checkValidationStatus()"
                      (hasPosition)="hasPositions($event)"
                      [dailyExpenseTypes]="dailyExpenseTypes"
                      [monthlyExpenseTypes]="monthlyExpenseTypes"
                      [validateProject]="validateProject"
                      [projectId]="projectId"
                      [projectData]="project"
                      [customerId]="clientId"
                      (callProjections)="callProjectProjectionApi(true)"
                      (callGetProjectAPI)="getUpdateProjectData()"
                      (firstStep)="goToNextStep('1')"
                      (thirdStep)="goToThirdStep()"
                      [dateObj]="dateObj"
                      [clientId]="clientId"
                      (purchaseOrderSidebar)="openPurchaseOrderSidebar()"
                      [listOFPurchaseOrder]="listOFPurchaseOrder"
                      [financialCalculation]="recalculating"
                      [currentProjectState]="currentProjectState"
                    >
                    </app-position-setup>
                    <!--end: Form Wizard Step 2-->

                    <!--begin: Form Wizard Step 3-->
                    <form class="form mt-0" id="wizard3">
                      <div data-wizard-type="step-content position-relative">
                        <ng-container *ngIf="wizardStep === 3">
                          <app-financial-review
                            #financialReview
                            [minDate]="startDate"
                            [maxDate]="endDate"
                            (checkValidationStatus)="checkValidationStatus()"
                            [project]="project"
                            [projectId]="projectId"
                            [validationStatus]="validationStatus"
                          ></app-financial-review>
                          <div class="d-flex btn-footer justify-content-start fixed-content px-5 py-5">
                            <div type="button" class="btn btn-height btn-prev text-uppercase font-weight-bold px-9 py-4" data-wizard-type="action-prev" (click)="goToSecondStep()">
                              Prev
                            </div>
                          </div>
                        </ng-container>
                      </div>
                    </form>
                    <!--end: Form Wizard Step 3-->

                    <!--end: Form Wizard Form-->
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!--end: Wizard -->
        </div>
      </div>
    </mat-drawer-container>
  </mat-drawer-container>
</span>

<p-dialog
  header=""
  [(visible)]="showLeavePageDialog"
  [modal]="true"
  class="confirm-dialog-paushed"
  [baseZIndex]="10000"
  [draggable]="false"
  [resizable]="false"
  [closable]="false"
  [style]="{ width: '576px' }"
>
  <div class="ml-2">
    <div class="d-flex align-items-center">
      <h5 class="p-m-0 text-wrap">Financial calculations are paused while edits are in progress. Are you done editing and ready to recalculate?</h5>
    </div>
    <div class="p-field-checkbox d-flex align-items-start mt-2">
      <p-checkbox [(ngModel)]="rememberChoice" [binary]="true" inputId="rememberChoice"></p-checkbox>
      <label for="rememberChoice" class="ml-2 pl-2">Don't ask again</label>
    </div>
  </div>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-nowrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="leavePage()">No</button>
      <button type="button" class="btn-save" (click)="resumeAndStay()">Yes, recalculate</button>
    </div>
  </ng-template>
</p-dialog>
