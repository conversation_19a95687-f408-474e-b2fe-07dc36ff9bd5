import { POList<PERSON>bj, <PERSON><PERSON><PERSON><PERSON>, UpdateMultiPOObj, UpdateMultiPosDate } from './../project.model';
import {
  Component,
  OnInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Input,
  Output,
  EventEmitter,
  AfterViewInit,
  ViewChild,
  ElementRef,
  OnDestroy,
  SimpleChanges,
  OnChanges
} from '@angular/core';
import { ConfirmationService, MessageService } from 'primeng/api';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { OGantryHttpResponse } from '@shared/models';
import { ClientService } from '@entities/client/client.service';
import {
  Employee,
  Employees,
  Position,
  PositionList,
  DailyExpenseType,
  AddExpense,
  PositionExpenseList,
  MonthlyExpenseType,
  ProjectStartEndDateObj,
  DataFilterForPositions
} from '../project.model';
import { ProjectService } from '@entities/project/project.service';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { AlertType } from '@shared/models/alert-type.enum';
import { DatePipe } from '@angular/common';
import { forkJoin, Observable, Subject, Subscription } from 'rxjs';
import moment from 'moment';
import { AdministrationService } from '@entities/administration/administration.service';
import { forbiddenNameValidator } from '@shared/directives/validators.directive';
import { LayoutConfigService } from '@shared/services/layout-config.service';
import { AppConstants } from '@shared/constants';
import { MatDialog } from '@angular/material/dialog';
import { AppendTagsComponent } from '@entities/administration/append-tags/append-tags.component';
import { UtilizationService } from '@entities/utilization-management/utilization.service';
import { ColumnToggleService } from '@shared/services/column-toggle.service';
import { ComponentsType, FiledType } from '@shared/models/component-type-enum';
import { filter, tap } from 'rxjs/operators';

import { Table } from 'primeng/table';
import { ProjectUserState, PurchaseOrderList } from '@shared/models/custom/purchase-order.model';
import { Debounce } from '@shared/decorators/debounce.decorator';
import { GetSetCacheFiltersService } from '@shared/services/get-set-cache-filters.service';
import { AddCommasToNumbersPipe } from '@shared/pipes/add-commas-to-numbers.pipe';
import { HttpErrorResponse } from '@angular/common/http';

const expenses = [
  { label: 'Daily', value: 'daily' },
  { label: 'Monthly', value: 'monthly' }
];

enum Expenses {
  MONTHLY = 'monthly',
  DAILY = 'daily'
}
@Component({
  selector: 'app-position-setup',
  templateUrl: './position-setup.component.html',
  styleUrls: ['./position-setup.component.scss'],
  providers: [MessageService, DatePipe, ConfirmationService, AddCommasToNumbersPipe]
})
export class PositionSetupComponent extends SflBaseComponent implements OnInit, AfterViewInit, OnChanges, OnDestroy {
  openedPosition: Position;
  @Input() financialCalculation = false;
  @Input() currentProjectState: ProjectUserState;
  currentProjectStateValue = ProjectUserState;
  expenseChangedPositions: Array<any> = [];
  @Input() isFinanceCalculationStopped = false;
  @ViewChild('customMonthCalender', { read: ElementRef }) p_calender: ElementRef<any>;
  @ViewChild('dt') table: Table;
  selectedDates: Date[] = [];
  weeklyHours = 0;
  @Input() listOFPurchaseOrder: PurchaseOrderList | Array<any> = [];
  isShowHideColumns = false;
  frozenCols = [];
  filedType = FiledType;
  @Input() dateObj: ProjectStartEndDateObj = {} as ProjectStartEndDateObj;
  minDateOfProject = new Date();
  updatePositionDate: FormGroup;
  showPositionUpdateDateDialog = false;
  showPOUpdateDialog = false;
  checkedPosition: Position[] = [];
  listSelected = false;
  cloneEditPosition: Position[] = [];
  positionIsUnique = true;
  editPositionIsUnique = true;
  position: Position[] = [];
  loading = true;
  showExpenseModal = false;
  addNewExpense = [{ expense: null, type: null, amount: null }];
  employeeList: Employee[] = [{ label: '-- None --', id: 0 }];
  addPositionForm: FormGroup;
  expenses = expenses;
  duplicatePostionIsSave = true;
  duplicatePostionClone: Partial<Position> | undefined;
  expenseEnum = Expenses;
  showDailyExpenseType = false;
  allowExpenseSelection = false;
  @Input() projectId: number;
  @Input() clientId: number | null;
  @Input() projectData: any;
  openFilter = false;
  @Input() customerId: number;
  addExpenseForm: FormGroup;
  date: Date;
  showDeleteDialog = false;
  deletePositionId: number;
  editPositionObj: Position = new Position();
  clonePosition: { [s: string]: Position } = {};
  showCostError = false;
  showAllocationError = false;
  @Input() dailyExpenseTypes: DailyExpenseType[];
  @Input() monthlyExpenseTypes: MonthlyExpenseType[];
  positionExpenseList: PositionExpenseList[] = [];
  editExpenseObject: AddExpense = new AddExpense();
  cloneExpense: { [s: string]: PositionExpenseList } = {};
  showDescriptionError = false;
  positionId: number;
  employeeId: number;
  expenseLoader = false;
  @Output() callProjections = new EventEmitter();
  @Output() firstStep = new EventEmitter();
  @Output() thirdStep = new EventEmitter();
  @Output() callGetProjectAPI = new EventEmitter();
  @Output() purchaseOrderSidebar = new EventEmitter();
  showBillRate = false;
  showPositionError: string;
  showBillRateError = false;
  @Input() validateProject: boolean;
  showExpenseDeleteDialog = false;
  deleteExpenseObj = null;
  maxDate = null;
  minDate = null;
  positionMinDate = null;
  yearRange = null;
  showAddPosition = false;
  skillSetList = [];
  height = AppConstants.height;
  startDate;
  endDate;
  positionEdit = false;
  currentlyEditingRowIndex: number | null = null;
  _selectedColumns: any = [];
  _pCols: string[] = [];
  isShowEmployeeError = {
    isErrorInAdd: false,
    isErrorInEdit: false
  };
  isShowCostError = {
    isErrorInAdd: false,
    isErrorInEdit: false
  };
  empStartDate: Date;
  empEndDate: Date;
  editEmpStartDate: Date;
  editEmpEndDate: Date;
  empCost = 0;
  empCostEditTime = 0;
  checkEmpErrorFor = {
    editPosition: 'editPosition',
    addPosition: 'addPosition'
  };
  showCostDifferentDialog = false;
  showInvalidEmpDialog = false;
  sortColumnFlag = false;
  sortFieldName: string = 'name';
  sortOrderNumber: number = 1;
  purchaseOrderIdForMultiPos: number;
  selectedMonths: Date[] = [];
  selectedMonth: SelectedMonth[] = [];
  @Input() get selectedColumns(): any[] {
    return this._selectedColumns;
  }

  set selectedColumns(val: any) {
    setTimeout(() => {
      this.columnToggle.setSelectedColumns(this._selectedColumns, 'Postion');
      const col = this._selectedColumns;
      if (col) {
        this._selectedColumns = col.filter((val) => val.includes(val));
      } else {
        this._selectedColumns = this.frozenCols.filter((col) => val.includes(col));
      }
      this._pCols = col?.map((f) => f.field);
    }, 500);
  }

  @Output() hasPosition = new EventEmitter();
  @Output() checkValidationStatus = new EventEmitter();
  @ViewChild('focusAdd', { static: false })
  set focusAdd(element: ElementRef<HTMLInputElement>) {
    if (element) {
      element.nativeElement.focus();
    }
  }
  @ViewChild('focusEdit', { static: false })
  set focusEdit(element: ElementRef<HTMLInputElement>) {
    if (element) {
      element.nativeElement.focus();
    }
  }
  recalculating = false;
  updatingExpensesFor: Position[] = [];
  positionSelectedToBeUpdated: Position;
  duplicatedPositionObject: Position;
  editModeEnable = false;
  showDuplicatePosition = false;
  loadingMsg = 'Retrieving position....';
  isValidSelectedDate = true;
  componentType = ComponentsType.Position;
  extendFields: any;
  updateExtendFiled: string;
  showUpdateExtendFiledDialog = false;
  positionObj: Position;
  positionSetupForm: Position;
  totalColumns = 10;
  noOfColumns = 10;
  loadingSubscription: Subscription;
  isLoading = false;
  dataFilter: DataFilterForPositions = {} as DataFilterForPositions;
  showFilter = false;
  dateFilter = [];
  purchaseOrderList = [];
  isEditExpense = false;
  @ViewChild('dt1') dt1: Table;

  constructor(
    private readonly layoutConfigService: LayoutConfigService,
    private readonly adminService: AdministrationService,
    private readonly datePipe: DatePipe,
    public readonly projectService: ProjectService,
    private clientService: ClientService,
    private readonly cdf: ChangeDetectorRef,
    private readonly layoutUtilsService: LayoutUtilsService,
    private readonly dialog: MatDialog,
    private readonly confirmationService: ConfirmationService,
    private readonly utilizationService: UtilizationService,
    private readonly columnToggle: ColumnToggleService,
    private readonly addCommasToNumber: AddCommasToNumbersPipe,
    private readonly cacheFilter: GetSetCacheFiltersService
  ) {
    super();
  }

  ngOnInit() {
    this.showAddPosition = false;
    this.updatePostionColumn();
    this.updateDateFormInit();
    this.initForm();
    this.openFilter = true;
    this.listenRecalculatingRevenue();
    this.utilizationService.showTagsForEditPosition.subscribe((res) => {
      if (res) {
        this.getPositionList(true);
      }
    });
    this.getPositionList(true);
    this.getGlobalDetailsCategory();
    this.loadingSubscription = this.projectService.recalculatingTheRevenue$
      .pipe(
        tap((loading) => {
          if (!loading && this.isLoading) {
            this.getPositionList();
          }
          this.isLoading = loading;
        }),
        filter((loading) => loading === false)
      )
      .subscribe();
    this.totalColumns = this.noOfColumns + this._pCols.length;
    this.setDataFilterDefault();
    this.setDateFilter();
    this.listenChange();
    this.expenseChangedPositions = JSON.parse(localStorage.getItem('changedExpensePosition')) || [];
  }

  ngAfterViewInit() {
    this.layoutConfigService.updateHeight$.next(true);
    this.height = AppConstants.height;
    setTimeout(() => this.highlightSelectedMonths(), 100);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['customerId'] && this.customerId) {
      this.getPurchaseOrderList();
    }
    if (changes['financialCalculation']) {
      const newValue = changes['financialCalculation'].currentValue;
      this.onFinancialCalculationChanged(newValue);
    }
  }

  initForm() {
    this.addPositionForm = new FormGroup({
      name: new FormControl('', [Validators.required, forbiddenNameValidator(/^project_expenses$/)]),
      cost: new FormControl('', Validators.required),
      daily_billable_hours: new FormControl((8.0).toFixed(1), [Validators.max(8.0), Validators.min(0.0)]),
      start_date: new FormControl('', Validators.required),
      end_date: new FormControl('', Validators.required),
      employee_id: new FormControl(null),
      project_id: new FormControl(''),
      position_type_id: new FormControl('', [Validators.required]),
      bill_rate: new FormControl(''),
      purchase_order_id: new FormControl(null)
    });
    this.weeklyHours = ((this.addPositionForm.controls['daily_billable_hours'].value || 0) * 5) as number;

    this.addExpenseForm = new FormGroup({
      cost: new FormControl('', Validators.required),
      description: new FormControl('', Validators.required),
      date: new FormControl(this.date),
      month: new FormControl(''),
      year: new FormControl(''),
      type_id: new FormControl('', Validators.required),
      type: new FormControl('')
    });
    this.getSkillSets();
  }

  AddTags(res: any) {
    const dialogRef = this.dialog.open(AppendTagsComponent, {
      data: {
        tags: res.tags,
        positionId: res.id,
        title: 'Add Tag',
        flag: 'ADD TAGS TO POSITION'
      },
      width: '880px'
    });
  }

  getSkillSets() {
    this.subscriptionManager.add(
      this.adminService.getPositionTypes().subscribe((res) => {
        this.loading = false;
        if (res?.body?.data?.position_types?.length > 0) {
          res?.body?.data?.position_types?.forEach((e) => {
            this.skillSetList.push({
              label: e.position_type?.name,
              id: e.position_type.id
            });
          });
        }
      })
    );
  }

  activeSort(event: Table): string | null {
    if (event?.sortField) {
      if (event?.sortOrder === 1) {
        return `asc:${event.sortField}`;
      } else {
        return `desc:${event.sortField}`;
      }
    }
    return null;
  }

  getPurchaseOrderList(): void {
    if (!this.customerId) return;
    this.subscriptionManager.add(
      this.adminService.getPurchaseOrderByCustomerList(this.customerId).subscribe((res) => {
        this.loading = false;
        if (res?.data?.purchase_orders?.length > 0) {
          res?.data?.purchase_orders?.forEach((po) => {
            this.purchaseOrderList.push({
              label: po.purchase_order?.po_number || '',
              id: po.purchase_order?.id || ''
            });
          });
        }
      })
    );
  }

  @Debounce()
  getPositionList(dt?) {
    if (this.projectId) {
      this.loading = true;
      this.position = [];
      let param = {
        project_id: this.projectId,
        visibility: 'Public',
        order_by: !this.sortColumnFlag ? (this.dataFilter?.order_by ? this.dataFilter.order_by : this.activeSort(this.table)) : this.activeSort(this.table)
      };

      if (param['order_by']) {
        this.dataFilter.order_by = param['order_by'];
      } else {
        this.dataFilter.order_by = param['order_by'] = 'asc:name';
      }

      if (this.dataFilter?.position_type_id) {
        param['position_type_id'] = this.dataFilter.position_type_id;
      }
      if (this.dataFilter?.employee_id) {
        param['employee_id'] = this.dataFilter.employee_id;
      }
      if (this.dataFilter?.name) {
        param['name_search'] = this.dataFilter.name;
      }
      if (this.dataFilter?.cost) {
        param['cost'] = this.dataFilter.cost;
      }
      if (this.dataFilter?.daily_billable_hours) {
        param['daily_billable_hours'] = this.dataFilter.daily_billable_hours;
      }
      if (this.dataFilter?.bill_rate) {
        param['bill_rate'] = this.dataFilter.bill_rate;
      }

      if (this.dataFilter?.start_date) {
        const formattedStartDate = this.datePipe.transform(new Date(this.dataFilter?.start_date), this.appConstants.format);

        switch (this.dataFilter?.selectedFilterStartDate) {
          case 'gte':
            param['start_date_gte'] = formattedStartDate;
            break;
          case 'lt':
            param['start_date_lt'] = formattedStartDate;
            break;
          default:
            param['start_date'] = formattedStartDate;
            break;
        }
      }

      if (this.dataFilter?.end_date) {
        const formattedEndDate = this.datePipe.transform(new Date(this.dataFilter?.end_date), this.appConstants.format);

        switch (this.dataFilter?.selectedFilterEndDate) {
          case 'gte':
            param['end_date_gte'] = formattedEndDate;
            break;
          case 'lt':
            param['end_date_lt'] = formattedEndDate;
            break;
          default:
            param['end_date'] = formattedEndDate;
            break;
        }
      }

      if (!this.sortColumnFlag) {
        this.sortFieldName = this.dataFilter?.order_by ? this.dataFilter.order_by.split(':')[1] : this.table?.sortField;
        this.sortOrderNumber = this.dataFilter?.order_by ? (this.dataFilter.order_by?.split(':')[0] === 'asc' ? 1 : -1) : this.table?.sortOrder;
      }

      this.subscriptionManager.add(
        this.projectService.getPositionList(param).subscribe(
          async (res: OGantryHttpResponse<PositionList>) => {
            if (res.message === 'No results found') {
              this.loadingMsg = 'No Positions found.';
            }
            if (res?.data?.positions) {
              this.position = res?.data?.positions;
              this.position?.forEach((position) => {
                position.position.margin = Number(position?.position?.total?.percent_gross_margin) * 100;
                position.position.expense = Number(position?.position?.total?.expenses);
              });
            }
            this.cloneEditPosition = JSON.parse(JSON.stringify(this.position));
            this.hasPosition.emit(res.data.positions);
            this.loading = false;

            if (!this.duplicatePostionIsSave) {
              this.position.push(this.duplicatePostionClone);
              this.table?.initRowEdit(this.duplicatePostionClone);
            } else {
              this.positionEdit = false;
              this.currentlyEditingRowIndex = null;
            }
          },
          () => {
            this.loading = false;
            this.currentlyEditingRowIndex = null;
          }
        )
      );
    }
  }

  getExpenseValues(positions): Promise<Position[]> {
    return new Promise((resolve) => {
      const length = positions?.length;
      if (length) {
        const positionData = [...positions];
        positionData?.forEach(async (position, index) => {
          if (length) {
            await new Promise((res) => setTimeout(res, 400));
          }
          //
          position = await this.apiCall(position);
          if (index === positionData.length - 1) {
            resolve(positionData);
          }
        });
      } else {
        resolve([]);
      }
    });
  }

  apiCall(position): Promise<Position> {
    return new Promise((resolve) => {
      forkJoin([this.projectService.getDailyExpenses(position.position.id), this.projectService.getMonthlyExpenses(position.position.id)]).subscribe((res) => {
        // if (position.position.bill_rate !== '0.00') {
        position.position.margin = Number(position.position.total.percent_gross_margin) * 100;
        // }
        position.position.expense = Number(position.position.total.expenses);
        position.position.daily_expenses = res[0]?.data?.position_daily_expenses;
        position.position.monthly_expenses = res[1]?.data?.monthly_expenses;
        this.cdf.detectChanges();
        resolve(position);
      });
    });
  }

  getEmployeeList() {
    const queryFilter = {
      order_by: 'asc:first_name',
      employee_status: 'activefuture'
    };
    this.subscriptionManager.add(
      this.projectService.getEmployeeList(queryFilter).subscribe((res: OGantryHttpResponse<Employees>) => {
        res?.data?.employees?.forEach((e) => {
          this.employeeList.push({
            label: e.employee?.name,
            id: e.employee.id,
            cost: e?.employee?.hourly_cost
          });
        });
      })
    );
  }

  getStatus(status) {
    switch (status) {
      case 'Draft':
        return 'badge badge-draft';
      case 'Forecast':
        return 'badge badge-forecast';
      case 'Booked':
        return 'badge badge-booked';
      case 'Completed':
        return 'badge badge-completed';
    }
  }

  listenRecalculatingRevenue() {
    this.subscriptionManager.add(
      this.projectService.getRecalculatingTheRevenueSubject().subscribe((recalculating) => {
        this.recalculating = recalculating;
        if (!recalculating) {
          this.updatingExpensesFor = [];
        }
      })
    );
  }

  // will take position as param and will check in the updatingExpensesFor array to see if there is an entry for that position if yes that position's expense is being recalculated.
  checkIfThisExpenseIsBeingUpdating(position: Position) {
    if (this.updatingExpensesFor.length > 0 && position) {
      const foundPositions = this.updatingExpensesFor?.filter((pos) => pos?.position?.id === position?.position?.id || pos?.position?.pid === position?.position?.pid);
      return foundPositions.length ?? false;
    } else {
      return false;
    }
  }

  async showAddExpenseModal(position: Position) {
    if (!this.showDuplicatePosition) {
      this.positionSelectedToBeUpdated = position;
      this.positionExpenseList = [];
      await this.apiCall(position);
      position?.position?.daily_expenses?.forEach((expense) => {
        this.positionExpenseList.push({
          ...expense.position_daily_expense,
          key: 'Daily'
        });
      });
      position?.position?.monthly_expenses?.forEach((expense) => {
        this.positionExpenseList.push({
          ...expense.position_monthly_expense,
          key: 'Monthly'
        });
      });
      this.openedPosition = position;
      this.isEditExpense = false;
      this.showExpenseModal = !this.showExpenseModal;
      this.positionId = position.position.id;
      this.employeeId = Number(position?.position?.employee?.id);
      this.maxDate = new Date(position.position.end_date);
      this.minDate = new Date(position.position.start_date);
      this.yearRange = this.minDate.getFullYear() + ':' + this.maxDate.getFullYear();
      this.cdf.detectChanges();
      return position;
    }
  }

  closeModal() {
    this.getPositionList();
    this.showExpenseModal = false;
    this.showDailyExpenseType = false;
    this.allowExpenseSelection = false;
    this.positionId = null;
    this.employeeId = null;
    this.addExpenseForm.reset();
    this.editExpenseObject = new AddExpense();
    this.positionExpenseList = [];
    this.maxDate = null;
    this.minDate = null;
    this.yearRange = null;
    if (this.dt1?.editingRowKeys) {
      this.dt1.editingRowKeys = {};
    }
    this.isEditExpense = false;
    this.showCostError = false;
    this.showDescriptionError = false;
    this.resetExpenseMonthValue();
  }

  addNewPosition(addDuplicatePosition?: any, dt?) {
    const positionList = this.position;
    let duplicateObj = {};
    if (addDuplicatePosition) {
      const employeedId = addDuplicatePosition?.employee?.id ? Number(addDuplicatePosition?.employee?.id?.id) : null;
      duplicateObj = {
        name: addDuplicatePosition.name,
        start_date: addDuplicatePosition.start_date,
        end_date: addDuplicatePosition.end_date,
        position_type_id: addDuplicatePosition.position_type_id.id || null,
        employee_id: this.employeeList.filter((emp) => emp.id === employeedId)[0],
        bill_rate: addDuplicatePosition.bill_rate === 'n/a' ? 0 : addDuplicatePosition.bill_rate,
        cost: addDuplicatePosition.cost,
        daily_billable_hours: addDuplicatePosition.daily_billable_hours,
        purchase_order_id: addDuplicatePosition?.purchase_order_id ?? null
      };
      this.positionEdit = false;
    }

    let flag = false;
    const createPositionObj = addDuplicatePosition ? duplicateObj : this.addPositionForm.value;
    createPositionObj.project_id = this.projectId;

    if (!addDuplicatePosition) {
      flag = this.checkFormForValidation(this.addPositionForm);
      createPositionObj.position_type_id = createPositionObj?.position_type_id?.id;
    }
    if (
      !flag &&
      !(this.isShowEmployeeError.isErrorInAdd && !addDuplicatePosition) &&
      !(this.isShowEmployeeError.isErrorInEdit && addDuplicatePosition) &&
      !(this.isShowCostError.isErrorInAdd || this.isShowCostError.isErrorInEdit)
    ) {
      const empCostForAddDuplicate = addDuplicatePosition ? this.empCostEditTime : this.empCost;
      const isCostChanged = parseFloat(parseFloat(createPositionObj.cost).toFixed(2)) !== parseFloat(empCostForAddDuplicate.toFixed(2)) && createPositionObj?.employee_id?.id;

      if (isCostChanged && createPositionObj.employee_id) {
        this.showAlert({}, createPositionObj.cost, empCostForAddDuplicate).subscribe((res) => {
          if (res === false) {
            createPositionObj.cost = this.empCost;
          }
          this.createNewPosition(createPositionObj, positionList, addDuplicatePosition);
        });
      } else {
        this.createNewPosition(createPositionObj, positionList, addDuplicatePosition);
      }
    }
  }

  createNewPosition(createPositionObj: any, positionList: Position[], addDuplicatePosition: any): void {
    this.startDate = createPositionObj.start_date = this.datePipe.transform(new Date(createPositionObj.start_date), AppConstants.format);
    this.endDate = createPositionObj.end_date = this.datePipe.transform(new Date(createPositionObj.end_date), AppConstants.format);
    if (!createPositionObj.bill_rate) {
      delete createPositionObj.bill_rate;
    }
    createPositionObj.employee_id = createPositionObj?.employee_id?.id === 0 ? null : createPositionObj?.employee_id?.id;
    if (this.positionIsUnique) {
      this.loading = true;
      this.position = [];
      this.subscriptionManager.add(
        this.projectService.createNewPosition(createPositionObj).subscribe(
          (res) => {
            this.duplicatedPositionObject = res?.data;
            if (addDuplicatePosition) {
              this.showDuplicatePosition = false;
            }
            this.getPositionList();
            this.cdf.detectChanges();
            this.initForm();
            this.callGetProjectAPI.emit();
            this.callProjections.emit();
            this.checkValidationStatus.emit();
            this.validateProject = true;
            this.showAddPosition = false;
            this.isShowEmployeeError.isErrorInAdd = this.isShowEmployeeError.isErrorInEdit = false;
            this.isShowCostError.isErrorInAdd = this.isShowCostError.isErrorInEdit = false;
            this.positionMinDate = null;
            this.layoutUtilsService.showActionNotification('Position added successfully', AlertType.Success);
          },
          () => {
            this.loading = false;
            this.position = positionList;
            this.cdf.detectChanges();
          }
        )
      );
    }
  }

  expenseSelected(value) {
    this.allowExpenseSelection = true;
    if (value.value === this.expenseEnum.DAILY) {
      this.showDailyExpenseType = true;
      this.addExpenseForm.controls['date'].setValidators([]);
      this.addExpenseForm.get('date').updateValueAndValidity();
    } else {
      this.showDailyExpenseType = false;
      this.selectedMonth = [];
      this.addExpenseForm.get('type_id').updateValueAndValidity();
    }
  }

  addExpense() {
    if (!this.checkFormForValidation(this.addExpenseForm)) {
      const addExpenseForm = this.addExpenseForm.value;
      if (addExpenseForm.type === this.expenseEnum.MONTHLY && !this.selectedMonths?.length) {
        return;
      }
      this.expenseLoader = true;
      this.addToExpenseChangedPositionList(this.openedPosition);
      const expenseList = this.positionExpenseList;
      this.positionExpenseList = [];
      let addExpenseServcie = '';
      let addExpenseObj = {};
      if (addExpenseForm.type === this.expenseEnum.DAILY) {
        addExpenseObj = {
          cost: addExpenseForm.cost,
          description: addExpenseForm.description,
          employee_id: this.employeeId,
          position_id: this.positionId,
          type_id: addExpenseForm.type_id
        };
        addExpenseServcie = 'addDailyExpense';
      } else {
        addExpenseServcie = 'addMonthlyExpense';
        if (this.selectedMonths?.length && this.selectedMonth) {
          const addExpenseObjectAPIcall = this.readyMultipleAPIforAddExpense(addExpenseForm, addExpenseServcie);
          this.addMultipleExpense(addExpenseObjectAPIcall, expenseList);
        }
      }
      if (addExpenseForm.type === this.expenseEnum.DAILY) {
        this.subscriptionManager.add(
          this.projectService[addExpenseServcie](addExpenseObj).subscribe({
            next: (res) => {
              this.updatingExpensesFor.push(this.positionSelectedToBeUpdated);
              this.expenseLoader = false;
              this.positionExpenseList = expenseList;
              if (res?.data?.position_daily_expense) {
                this.positionExpenseList.push({
                  ...res?.data?.position_daily_expense,
                  key: 'Daily'
                });
              }
              this.layoutUtilsService.showActionNotification(this.appConstants.addExpense, AlertType.Success);
              this.resetFilter();
            },
            error: () => {
              this.expenseLoader = false;
              this.positionExpenseList = expenseList;
              this.cdf.detectChanges();
            }
          })
        );
      }
    }
  }

  //get No of Weeks
  getNoOfWeeks(position) {
    if (position.start_date && position.end_date) {
      const startDate = new Date(position.start_date);
      const endDate = new Date(position.end_date);
      const difference_In_Time = endDate.getTime() - startDate.getTime();
      const difference_In_Days = difference_In_Time / (1000 * 3600 * 24);
      return Math.ceil(difference_In_Days / 7) + ' Weeks';
    }
  }

  duplicatePosition(position, dt) {
    this.duplicatePositionBYId(position.position.id, dt);
    // further use case local duplication
    // const rowIndex = this.position.indexOf(position);
    // if (!this.canEditRow(rowIndex)) {
    //   return;
    // }
    // this.showFilter = false;
    // this.addColumn();
    // this.totalColumns = this.noOfColumns + this._pCols.length;
    // this.isShowHideColumns = false;
    // this.positionEdit = true;
    // this.editPositionObj = JSON.parse(JSON.stringify(position));
    // this.currentlyEditingRowIndex = this.position.length;
    // this.editPositionObj.position.start_date = moment.utc(new Date(this.editPositionObj.position.start_date)).format('l LT');
    // this.editPositionObj.position.end_date = moment.utc(new Date(this.editPositionObj.position.end_date)).format('l LT');
    // const employeedId = position?.position?.employee ? Number(position?.position?.employee?.id) : null;
    // this.editPositionObj.position.employee.id = this.employeeList.filter((emp) => emp.id === employeedId)[0];
    // this.editPositionObj.position.position_type_id = this.skillSetList.filter((skill) => skill.label === position.position.type)[0];
    // this.positionMinDate = new Date(this.editPositionObj.position.start_date);
    // this.editPositionObj.position.id = null;
    // if (this.editPositionObj?.position?.employee?.id?.id === 0) {
    //   this.editPositionObj.position.employee.id = null;
    // }
    // this.editPositionObj.position.name = this.editPositionObj.position.name + '(Copy)';
    // this.editPositionObj['expense'] = 0;
    // this.editPositionObj.position.purchase_order_id = this.editPositionObj?.position?.purchase_order?.id ?? null;
    // const duplicateObj: any = {
    //   position: {
    //     ...this.editPositionObj
    //   }
    // };
    // this.position.push(duplicateObj);
    // this.cdf.detectChanges();
    // this.duplicatePostionClone = JSON.parse(JSON.stringify(duplicateObj));
    // this.duplicatePostionIsSave = false;
    // dt.initRowEdit(duplicateObj);
    // this.showDuplicatePosition = true;
    // const employee = {
    //   value: {
    //     id: this.editPositionObj?.position?.employee?.id?.id
    //   }
    // };
    // this.employeeSelected(employee, true);
  }

  deleteDuplicatedPosition(position, index) {
    this.position.splice(index, 1);
    this.positionEdit = false;
    this.showDuplicatePosition = false;
    this.isShowEmployeeError.isErrorInEdit = false;
    this.isShowCostError.isErrorInEdit = false;
    this.duplicatePostionIsSave = true;
    this.duplicatePostionClone = undefined;
    this.currentlyEditingRowIndex = null;
  }

  handleSaveOrAddNewPosition(positionData: any, dt?: any) {
    if (this.showDuplicatePosition) {
      this.addNewPosition(positionData, dt);
      this.duplicatePostionIsSave = true;
      this.duplicatePostionClone = undefined;
    } else {
      this.saveEditPosition();
    }
    this.currentlyEditingRowIndex = null;
  }

  handleCancelOrDeleteDuplicatedPosition(position: any, ri: any) {
    if (this.showDuplicatePosition) {
      this.deleteDuplicatedPosition(position, ri);
    } else {
      this.cancelEditPosition(position, ri);
    }
  }

  deletePosition() {
    this.isSubmitting = true;
    const positions = this.position;
    const id = this.deletePositionId;
    this.subscriptionManager.add(
      this.projectService.deletePosition(id).subscribe(
        () => {
          this.isSubmitting = false;
          this.closeDeleteModal();
          this.position = positions.filter((p) => p.position.id !== id);
          this.hasPosition.emit(this.position);
          this.checkValidationStatus.emit();
          this.cdf.detectChanges();
          this.layoutUtilsService.showActionNotification('Position has been deleted successsfully', AlertType.Success);
        },
        () => {
          this.isSubmitting = false;
          this.cdf.detectChanges();
        }
      )
    );
  }

  confirmDeleteProject(id: number) {
    this.deletePositionId = id;
    this.showDeleteDialog = true;
  }

  closeDeleteModal() {
    this.deletePositionId = null;
    this.showDeleteDialog = false;
    this.deleteExpenseObj = null;
    this.showExpenseDeleteDialog = false;
  }

  canEditRow(rowIndex: number): boolean {
    return (this.currentlyEditingRowIndex === null || this.currentlyEditingRowIndex === rowIndex) && !this.showAddPosition;
  }

  editPosition(position, dt?) {
    const rowIndex = this.position.indexOf(position);
    if (!this.canEditRow(rowIndex)) {
      return;
    }

    this.showFilter = false;
    this.addColumn();
    this.totalColumns = this.noOfColumns + this._pCols.length;
    this.editPositionIsUnique = true;
    this.positionEdit = true;
    this.isShowHideColumns = false;
    this.currentlyEditingRowIndex = rowIndex;
    this.positionSelectedToBeUpdated = position;
    this.clonePosition[position.position.id] = JSON.parse(JSON.stringify(position));
    this.editPositionObj = { ...position };
    this.editPositionObj.position.purchase_order_id = this.editPositionObj?.position?.purchase_order?.id ?? null;
    this.editPositionObj.position.start_date = moment.utc(new Date(this.editPositionObj.position.start_date)).format('l LT');
    this.editPositionObj.position.end_date = moment.utc(new Date(this.editPositionObj.position.end_date)).format('l LT');
    const employeedId = position?.position?.employee ? Number(position?.position?.employee?.id) : null;
    this.editPositionObj.position.employee.id = this.employeeList.filter((emp) => emp.id === employeedId)[0];
    this.editPositionObj.position.position_type_id = this.skillSetList.filter((skill) => skill.label === position.position.type)[0];
    this.editPositionObj.position.bill_rate = position?.position.bill_rate === 'n/a' ? 0 : position?.position.bill_rate;
    this.positionMinDate = new Date(this.editPositionObj.position.start_date);
    if (this.editPositionObj.position.employee.id.id === 0) {
      this.editPositionObj.position.employee.id = null;
    }
    this.empCostEditTime = parseFloat(parseFloat(position?.position?.cost).toFixed(2));
    dt?.initRowEdit(this.editPositionObj);
    this.editModeEnable = false;
    const employee = {
      value: {
        id: this.editPositionObj?.position?.employee?.id?.id
      }
    };
    this.employeeSelected(employee, true, true);
  }

  saveEditPosition(extendFiled: boolean = false) {
    if (
      (!this.showAllocationError &&
        this.editPositionObj?.position?.name.length >= 5 &&
        !this.showCostError &&
        !this.isShowCostError.isErrorInEdit &&
        !this.showBillRateError &&
        this.editPositionIsUnique &&
        !this.isShowEmployeeError.isErrorInEdit) ||
      extendFiled
    ) {
      const positionList = this.position;
      this.loading = true;
      this.position = [];
      let saveEditPosition;
      if (!extendFiled) {
        saveEditPosition = {
          cost: this.editPositionObj?.position?.cost,
          employee_id:
            this.editPositionObj?.position?.employee?.id === 0
              ? null
              : this.editPositionObj?.position?.employee?.id?.id === 0
              ? null
              : this.editPositionObj?.position?.employee?.id?.id
              ? this.editPositionObj?.position?.employee?.id?.id
              : this.editPositionObj?.position?.employee?.id,
          daily_billable_hours: this.editPositionObj.position.daily_billable_hours,
          id: this.editPositionObj.position.id,
          name: this.editPositionObj.position.name,
          start_date: this.datePipe.transform(new Date(this.editPositionObj.position.start_date), AppConstants.format),
          end_date: this.datePipe.transform(new Date(this.editPositionObj.position.end_date), AppConstants.format),
          bill_rate: this.editPositionObj?.position?.bill_rate === 'n/a' ? 0 : this.editPositionObj?.position?.bill_rate,
          position_type_id: this.editPositionObj?.position?.position_type_id?.id,
          extended_fields: this.positionObj?.position?.extended_fields,
          purchase_order_id: this.editPositionObj?.position?.purchase_order_id ?? null
        };
      } else {
        saveEditPosition = {
          id: this.editPositionObj.position.id,
          // position_type_id: this.editPositionObj?.position?.position_type_id?.id,
          extended_fields: this.positionObj?.position?.extended_fields
        };
      }

      if (!saveEditPosition?.position_type_id && !extendFiled) {
        saveEditPosition.position_type_id = this.skillSetList.find((skill) => skill.label === this.editPositionObj?.position?.type)?.id;
      }

      const isCostChanged = this.empCostEditTime !== parseFloat(saveEditPosition?.cost) && saveEditPosition.employee_id;

      if (isCostChanged) {
        this.showAlert({}, saveEditPosition.cost, this.empCostEditTime).subscribe((res) => {
          if (res === false) {
            saveEditPosition.cost = this.empCostEditTime;
          }
          this.onSaveEditPosition(saveEditPosition, extendFiled, positionList);
        });
      } else {
        this.onSaveEditPosition(saveEditPosition, extendFiled, positionList);
      }
    } else {
      if (!this.editPositionObj?.position?.name) {
        this.showPositionError = 'Position is required';
      } else if (this.editPositionObj?.position?.name.length < 5) {
        this.showPositionError = 'Position must be at least 5 characters in length';
      } else if (!this.editPositionIsUnique) {
      } else {
        this.showPositionError = '';
      }
      this.editPositionObj.position.bill_rate = this.editPositionObj?.position?.bill_rate.toString();
      if (!this.editPositionObj?.position?.bill_rate) {
        this.showBillRateError = true;
      }
      if (!this.editPositionObj?.position?.cost && !this.editPositionObj?.position?.employee?.id) {
        this.showCostError = true;
      }
    }
  }

  onSaveEditPosition(saveEditPosition: Position, extendFiled: boolean = false, positionList: Position[]) {
    this.subscriptionManager.add(
      this.projectService.updatePosition(saveEditPosition).subscribe(
        () => {
          this.updatingExpensesFor.push(this.positionSelectedToBeUpdated);
          this.getPositionList();
          this.positionEdit = false;
          this.currentlyEditingRowIndex = null;
          if (!extendFiled) {
            this.callGetProjectAPI.emit();
            this.callProjections.emit();
            this.checkValidationStatus.emit();
          }
          this.positionMinDate = null;
          this.closeExtendFiledPopup();
          this.layoutUtilsService.showActionNotification('Position updated successfully', AlertType.Success);
          this.cdf.detectChanges();
        },
        () => {
          this.loading = false;
          this.position = positionList;
        }
      )
    );
  }

  cancelEditPosition(position, index) {
    this.position[index] = this.clonePosition[position.position.id];
    this.showCostError = false;
    this.positionMinDate = null;
    this.showAllocationError = false;
    this.positionEdit = false;
    this.currentlyEditingRowIndex = null;
    this.isShowEmployeeError.isErrorInEdit = false;
    this.isShowCostError.isErrorInEdit = false;
    delete this.clonePosition[position.position.id];
  }

  editExpense(expense, index) {
    this.isEditExpense = true;
    this.editExpenseObject = {
      id: expense.id,
      cost: expense.cost,
      description: expense.description,
      type_id: expense.key === 'Daily' ? expense?.type?.id : expense?.expense_type?.id,
      date: new Date(expense?.year, expense?.month - 1),
      type: expense.key
    };
    this.cloneExpense[expense.id] = JSON.parse(JSON.stringify(expense));
  }
  saveEditExpense(index) {
    if (this.editExpenseObject.cost && this.editExpenseObject.description) {
      const expenseData = JSON.parse(JSON.stringify(this.positionExpenseList));
      this.expenseLoader = true;
      this.addToExpenseChangedPositionList(this.openedPosition);
      this.positionExpenseList = [];
      let saveEditExpense = {};
      let editExpenseService = '';
      if (this.editExpenseObject.type === 'Daily') {
        saveEditExpense = {
          cost: this.editExpenseObject.cost,
          description: this.editExpenseObject.description,
          type_id: this.editExpenseObject.type_id,
          employee_id: this.employeeId,
          position_id: this.positionId
        };
        editExpenseService = 'updateDailyExpense';
      } else {
        saveEditExpense = {
          cost: this.editExpenseObject.cost,
          description: this.editExpenseObject.description,
          month: this.editExpenseObject.date ? new Date(this.editExpenseObject.date).getMonth() + 1 : '',
          year: this.editExpenseObject.date ? new Date(this.editExpenseObject.date).getFullYear() : '',
          position_id: this.positionId,
          type_id: this.editExpenseObject.type_id
        };
        editExpenseService = 'updateMonthlyExpense';
      }

      this.subscriptionManager.add(
        this.projectService[editExpenseService](this.editExpenseObject.id, saveEditExpense).subscribe(
          (res) => {
            this.updatingExpensesFor.push(this.positionSelectedToBeUpdated);
            this.expenseLoader = false;
            this.isEditExpense = false;
            this.positionExpenseList = expenseData;
            if (res?.data?.position_daily_expense) {
              this.positionExpenseList[index] = {
                ...res?.data?.position_daily_expense,
                key: 'Daily'
              };
            } else if (res?.data?.position_monthly_expense) {
              this.positionExpenseList[index] = {
                ...res?.data?.position_monthly_expense,
                key: 'Monthly'
              };
            }
            this.checkValidationStatus.emit();
            this.cdf.detectChanges();
            this.layoutUtilsService.showActionNotification('Expenses updated successfully', AlertType.Success);
          },
          () => {
            this.loading = false;
          }
        )
      );
    } else {
      if (!this.editExpenseObject.cost) {
        this.showCostError = true;
      } else {
        this.showDescriptionError = true;
      }
    }
  }

  cancelEditExpense(expense, index) {
    this.isEditExpense = false;
    this.showCostError = false;
    this.showDescriptionError = false;
    this.positionExpenseList[index] = this.cloneExpense[expense.id];
    delete this.cloneExpense[expense.id];
  }

  expenseCostChange() {
    this.showCostError = !this.editExpenseObject.cost;
  }

  expenseDescChange() {
    this.showDescriptionError = !this.editExpenseObject.description;
  }

  costValueChange(event) {
    if (!this.editPositionObj?.position?.employee?.id) {
      if (event.target.ariaValueNow == "'null'") {
        this.showCostError = true;
      } else {
        this.showCostError = false;
      }
    }
  }

  billRateValueChange() {
    if (this.editPositionObj.position.bill_rate.length === 0) {
      this.showBillRateError = true;
      this.editPositionObj.position.bill_rate = null;
    } else {
      this.showBillRateError = false;
    }
  }

  positionValueChange(event: string, oldID: number) {
    if (!event) {
      this.showPositionError = 'Position is required';
    } else if (event.length < 5) {
      this.showPositionError = 'Position must be at least 5 characters in length';
    } else {
      this.showPositionError = '';
    }
    this.editPositionIsUnique = true;

    const oldPositionValue: Position[] = this.cloneEditPosition.filter((data) => {
      return data.position.id == oldID;
    });

    if (event !== oldPositionValue[0].position.name) {
      this.checkPotionIsUnique(true);
    }
  }

  positionFormAllocationValueChange() {
    const positionFormData = this.addPositionForm?.value;
    if (positionFormData.daily_billable_hours && !isNaN(positionFormData.daily_billable_hours)) {
      this.addPositionForm.controls['daily_billable_hours'].setValue(parseInt(positionFormData.daily_billable_hours));
    }
  }

  allocationValueChange(): void {
    if (this.editPositionObj?.position?.daily_billable_hours >= 0) {
      const hours = Number(this.editPositionObj.position.daily_billable_hours);
      this.editPositionObj.position.weekly_billable_hours = hours ? Number((hours * 5).toFixed(2)) : 0;
      if (hours >= 0 && hours <= 8) {
        this.showAllocationError = false;
      } else {
        this.showAllocationError = true;
      }
    } else {
      this.editPositionObj.position.weekly_billable_hours = 0;
      this.showAllocationError = true;
    }
  }

  getMonthName(monNumber) {
    return moment(monNumber, 'MM').format('MMM');
  }

  setBillRate(value) {
    this.showBillRate = value;
    if (value) {
      this.addPositionForm.controls['bill_rate'].setValidators(Validators.required);
      const moreColumn = [
        { field: 'Bill Rate', monthLabel: 'Hourly Bill Rate' },
        { field: 'Margin', monthLabel: 'Margin' }
      ];
      this.frozenCols = [...this.frozenCols, ...moreColumn];
      this.frozenCols = Array.from(new Set([...this.frozenCols, ...moreColumn]));
    } else {
      this.addPositionForm.controls['bill_rate'].setValidators([]);
    }
  }

  prevStep(): void {
    this.firstStep.emit();
  }

  nextStep() {
    this.thirdStep.emit();
  }

  clearStartDate() {
    this.addPositionForm.controls['start_date'].setValue('');
  }

  clearEndDate() {
    this.addPositionForm.controls['end_date'].setValue('');
  }

  resetFilter() {
    this.initForm();
    this.showAddPosition = false;
    this.showDailyExpenseType = false;
    this.allowExpenseSelection = false;
    this.isShowEmployeeError.isErrorInAdd = false;
    this.isShowCostError.isErrorInAdd = false;
    this.positionMinDate = null;
    this.layoutConfigService.updateHeight$.next(true);
    this.height = AppConstants.height;
    this.resetExpenseMonthValue();
    this.cdf.detectChanges();
  }

  employeeSelected(event, flag?: boolean, onClickEdit = false): void {
    if (event?.value?.id === 0) {
      flag ? (this.isShowEmployeeError.isErrorInEdit = false) : (this.isShowEmployeeError.isErrorInAdd = false);
      return;
    }
    let startDate = flag ? this.editPositionObj?.position?.start_date : this.addPositionForm.get('start_date').value;
    startDate = startDate ? new Date(startDate) : new Date();
    startDate = this.datePipe.transform(startDate, this.appConstants.format);
    this.subscriptionManager.add(
      this.projectService.getEmployeeActiveFinancial(event.value.id, startDate).subscribe((res) => {
        if (flag) {
          res.data.employee.start_date ? (this.editEmpStartDate = new Date(res.data.employee.start_date)) : '';
          res?.data?.employee?.end_date ? (this.editEmpEndDate = new Date(res?.data?.employee?.end_date)) : '';
          this.checkValidEmployeeForPosition(this.editPositionObj?.position?.start_date, this.editPositionObj?.position?.end_date, this.checkEmpErrorFor.editPosition);
          if (this.editPositionObj.position.employee.id.id === 0) {
            this.editPositionObj.position.employee.id = null;
            this.showCostError = !this.editPositionObj.position.cost;
            this.cdf.detectChanges();
          } else {
            this.showCostError = false;
            if (!onClickEdit) {
              this.editPositionObj.position.cost = res?.data?.employee?.hourly_cost;
              this.empCostEditTime = parseFloat(res?.data?.employee?.hourly_cost);
              this.editPositionObj?.position?.end_date &&
                this.getPositionCostOnEndDate(this.editPositionObj?.position?.employee?.id?.id, this.editPositionObj?.position?.end_date, this.checkEmpErrorFor.editPosition);
            }
            this.cdf.detectChanges();
          }
        } else {
          res?.data?.employee?.start_date ? (this.empStartDate = new Date(res?.data?.employee?.start_date)) : '';
          res?.data?.employee?.end_date ? (this.empEndDate = new Date(res?.data?.employee?.end_date)) : '';
          this.checkValidEmployeeForPosition(this.addPositionForm.get('start_date').value, this.addPositionForm.get('end_date').value, this.checkEmpErrorFor.addPosition);
          this.addPositionForm.value.employee_id.id &&
            this.addPositionForm.get('end_date').value &&
            this.getPositionCostOnEndDate(this.addPositionForm.value.employee_id.id, this.addPositionForm.get('end_date').value, this.checkEmpErrorFor.addPosition);
          res?.data?.employee?.hourly_cost ? (this.empCost = parseFloat(res?.data?.employee?.hourly_cost)) : '';
          if (this.addPositionForm.value.employee_id.id === 0) {
            this.addPositionForm.controls['employee_id'].reset();
            this.addPositionForm.controls['cost'].reset();
            this.addPositionForm.controls['cost'].setValidators(Validators.required);
            this.addPositionForm.get('cost').updateValueAndValidity();
            this.cdf.detectChanges();
          } else {
            this.addPositionForm.controls['cost'].setValue(res.data.employee.hourly_cost);
            this.addPositionForm.controls['cost'].setValidators([]);
            this.addPositionForm.get('cost').updateValueAndValidity();
            this.cdf.detectChanges();
          }
        }
      })
    );
  }

  getPositionCostOnEndDate(id: number, endDate: Date | string, checkErrorFor: string): void {
    // Todo: Uncomment this code if required to show pop-up for cost different.
    // if (!id) return;
    // const date = this.datePipe.transform(endDate, this.appConstants.format);
    // this.subscriptionManager.add(
    //   this.projectService.getEmployeeActiveFinancial(id, date).subscribe((res) => {
    //     const cost = parseFloat(res?.data?.employee?.hourly_cost);
    //     if (checkErrorFor === this.checkEmpErrorFor.addPosition) {
    //       const isError = cost !== parseFloat(this.addPositionForm.get('cost').value);
    //       this.isShowCostError.isErrorInAdd = isError;
    //       if (isError && !this.isShowEmployeeError.isErrorInAdd) {
    //         this.showCostDifferentDialog = true;
    //         this.cdf.detectChanges();
    //       }
    //     }
    //     if (checkErrorFor === this.checkEmpErrorFor.editPosition) {
    //       const isError = cost !== parseFloat(this.editPositionObj.position.cost);
    //       this.isShowCostError.isErrorInEdit = isError;
    //       if (isError && !this.isShowEmployeeError.isErrorInEdit) {
    //         this.showCostDifferentDialog = true;
    //         this.cdf.detectChanges();
    //       }
    //     }
    //   })
    // );
  }

  onCloseCostDifferentDialog(): void {
    this.showCostDifferentDialog = false;
  }

  onCloseInvalidEmpDialog(): void {
    this.showInvalidEmpDialog = false;
  }

  checkValidEmployeeForPosition(startDate: Date | string, endDate: Date | string, checkErrorFor: string): void {
    startDate = this.datePipe.transform(startDate, this.appConstants.format);
    endDate = this.datePipe.transform(endDate, this.appConstants.format);
    this.empStartDate = this.empStartDate ? new Date(this.datePipe.transform(this.empStartDate, this.appConstants.format)) : null;
    this.editEmpStartDate = this.editEmpStartDate ? new Date(this.datePipe.transform(this.editEmpStartDate, this.appConstants.format)) : null;
    this.empEndDate = this.empEndDate ? new Date(this.datePipe.transform(this.empEndDate, this.appConstants.format)) : null;
    this.editEmpEndDate = this.editEmpEndDate ? new Date(this.datePipe.transform(this.editEmpEndDate, this.appConstants.format)) : null;

    const addPositionError =
      (startDate && this.empStartDate && new Date(startDate) < new Date(this.empStartDate)) || (endDate && this.empEndDate && new Date(endDate) > new Date(this.empEndDate));
    const editPositionError =
      (startDate && this.editEmpStartDate && new Date(startDate) < new Date(this.editEmpStartDate)) ||
      (endDate && this.editEmpEndDate && new Date(endDate) > new Date(this.editEmpEndDate));

    if (addPositionError || editPositionError) {
      this.addPositionForm.get('employee_id')?.markAsTouched();
      this.addPositionForm.get('employee_id')?.markAsDirty();

      if (checkErrorFor === this.checkEmpErrorFor.addPosition && addPositionError) {
        this.isShowEmployeeError.isErrorInAdd = true;
        this.showInvalidEmpDialog = true;
      } else if (!addPositionError) {
        this.isShowEmployeeError.isErrorInAdd = false;
      }

      if (checkErrorFor === this.checkEmpErrorFor.editPosition && editPositionError) {
        this.isShowEmployeeError.isErrorInEdit = true;
        this.showInvalidEmpDialog = true;
      } else if (!editPositionError) {
        this.isShowEmployeeError.isErrorInEdit = false;
      }
    } else {
      if (checkErrorFor === this.checkEmpErrorFor.addPosition) {
        this.isShowEmployeeError.isErrorInAdd = false;
      }

      if (checkErrorFor === this.checkEmpErrorFor.editPosition) {
        this.isShowEmployeeError.isErrorInEdit = false;
      }
    }
  }

  showHelpData(data) {
    data.showHelpIconData = !data.showHelpIconData;
  }

  confirmDeleteExpense(expense) {
    this.showExpenseDeleteDialog = true;
    this.deleteExpenseObj = expense;
  }

  deleteExpense() {
    const expenseData = JSON.parse(JSON.stringify(this.positionExpenseList));
    this.expenseLoader = true;
    this.isSubmitting = true;
    this.positionExpenseList = [];
    let deleteExpenseService = '';
    if (this.deleteExpenseObj.key === 'Daily') {
      deleteExpenseService = 'deletePositionDailyExpense';
    } else {
      deleteExpenseService = 'deletePositionMonthlyExpense';
    }
    this.addToExpenseChangedPositionList(this.openedPosition);
    this.subscriptionManager.add(
      this.projectService[deleteExpenseService](this.deleteExpenseObj.id).subscribe(
        (res) => {
          this.expenseLoader = false;
          this.positionExpenseList = expenseData.filter((expense) => expense.id !== this.deleteExpenseObj.id);
          this.isSubmitting = false;
          this.updatingExpensesFor.push(this.positionSelectedToBeUpdated);
          this.checkValidationStatus.emit();
          this.closeDeleteModal();
          this.cdf.detectChanges();
          this.layoutUtilsService.showActionNotification('Expense deleted successfully', AlertType.Success);
        },
        () => {
          this.expenseLoader = false;
          this.isSubmitting = false;
          this.positionExpenseList = expenseData;
          this.cdf.detectChanges();
        }
      )
    );
  }

  showAddPositionRow() {
    this.showFilter = false;
    this.addColumn();
    this.totalColumns = this.noOfColumns + this._pCols.length;
    this.showAddPosition = true;
    this.isShowHideColumns = false;
    this.positionMinDate = null;
    this.editEmpStartDate = this.editEmpEndDate = null;
    this.empStartDate = this.empEndDate = null;
    this.layoutConfigService.updateHeight$.next(true);
    this.height = AppConstants.height;
    this.cdf.detectChanges();
  }

  startDateSelected(): void {
    this.positionMinDate = new Date(this.addPositionForm.value.start_date);
    if (this.positionMinDate > new Date(this.addPositionForm.value.end_date)) {
      this.addPositionForm.controls['end_date'].reset();
    }
    const employee = {
      value: {
        id: this.addPositionForm.get('employee_id')?.value?.id
      }
    };
    this.checkValidEmployeeForPosition(this.addPositionForm.get('start_date').value, this.addPositionForm.get('end_date').value, this.checkEmpErrorFor.addPosition);
    this.employeeSelected(employee, false);
  }

  endDateSelected(): void {
    const employee = {
      value: {
        id: this.addPositionForm.get('employee_id')?.value?.id
      }
    };
    this.checkValidEmployeeForPosition(this.addPositionForm.get('start_date').value, this.addPositionForm.get('end_date').value, this.checkEmpErrorFor.addPosition);
    this.getPositionCostOnEndDate(employee.value.id, this.addPositionForm.get('end_date').value, this.checkEmpErrorFor.addPosition);
  }

  editPositionStartDateSelected(): void {
    this.positionMinDate = new Date(this.editPositionObj.position.start_date);
    if (this.positionMinDate > new Date(this.editPositionObj.position.end_date)) {
      this.editPositionObj.position.end_date = null;
    }
    const employee = {
      value: {
        id: this.editPositionObj?.position?.employee?.id?.id
      }
    };
    this.checkValidEmployeeForPosition(this.editPositionObj?.position?.start_date, this.editPositionObj?.position?.end_date, this.checkEmpErrorFor.editPosition);
    this.employeeSelected(employee, true);
  }

  editPositionEndDateSelected(): void {
    const employee = {
      value: {
        id: this.editPositionObj?.position?.employee?.id?.id
      }
    };
    this.checkValidEmployeeForPosition(this.editPositionObj?.position?.start_date, this.editPositionObj?.position?.end_date, this.checkEmpErrorFor.editPosition);
    this.getPositionCostOnEndDate(employee.value.id, this.editPositionObj.position.end_date, this.checkEmpErrorFor.editPosition);
  }

  clearDate(fieldName: string) {
    switch (fieldName) {
      case 'start_date':
        this.editPositionObj.position.start_date = '';
        break;
      case 'end_date':
        this.editPositionObj.position.end_date = '';
        break;
    }
  }

  getStyle() {
    return {
      borderLeft: 'none',
      borderTopLeftRadius: '0px',
      borderBottomLeftRadius: '0px'
    };
  }

  checkPotionIsUnique(isEditMode: boolean = false): void {
    !isEditMode ? this.checkPositionNew() : this.checkPositionEditMode();
  }

  checkPositionEditMode(): void {
    const checkPosition = this.position.filter((data) => {
      return data.position.name.trim() == this.editPositionObj.position.name?.trim() && data.position.id != this.editPositionObj.position.id;
    });
    if (checkPosition?.length > 0) {
      checkPosition;
      this.editPositionIsUnique = false;
      this.showPositionError = 'Position must be Unique';
    } else {
      this.editPositionIsUnique = true;
      this.showPositionError = '';
    }
  }

  checkPositionNew(): void {
    const checkPosition = this.position.filter((data) => {
      return data.position.name?.trim() == this.addPositionForm.value.name?.trim();
    });

    if (checkPosition?.length > 0) {
      this.positionIsUnique = false;
    } else {
      this.positionIsUnique = true;
    }
  }

  selectAllPositionCheck(): void {
    this.checkedPosition = this.position;
  }

  removePosition(): void {
    this.checkedPosition = [];
  }
  listChecked(): void {
    this.checkedPosition.length ? (this.listSelected = true) : (this.listSelected = false);
  }

  updateDateFormInit(): void {
    this.updatePositionDate = new FormGroup({
      start_date: new FormControl(new Date(), Validators.required),
      end_date: new FormControl(new Date(), [Validators.required]),
      isUpdateStartDate: new FormControl(false),
      isUpdateEndDate: new FormControl(false)
    });
  }

  updatePostionDate(): void {
    if (this.updatePositionDate.valid && (this.updatePositionDate?.value?.isUpdateStartDate || this.updatePositionDate?.value?.isUpdateEndDate)) {
      let positionIds = '';
      let reqObj: UpdateMultiPosDate = {} as UpdateMultiPosDate;

      this.checkedPosition.forEach((position) => {
        if (positionIds === '') {
          positionIds += `${position.position.id}`;
        } else {
          positionIds += `,${position.position.id}`;
        }
      });
      reqObj.position_ids = positionIds;
      if (this.updatePositionDate?.value?.isUpdateStartDate) {
        reqObj.start_date = this.datePipe.transform(new Date(this.updatePositionDate.value.start_date), AppConstants.format);
      }
      if (this.updatePositionDate?.value?.isUpdateEndDate) {
        reqObj.end_date = this.datePipe.transform(new Date(this.updatePositionDate.value.end_date), AppConstants.format);
      }

      this.isSubmitting = true;
      this.projectService.updateDateForMultiPositions(reqObj).subscribe(
        (res: OGantryHttpResponse<PositionList>) => {
          if (res.success) {
            this.layoutUtilsService.showActionNotification(this.appConstants.positionDataSave, AlertType.Success);
            this.getPositionList();
            this.positionEdit = false;
            this.callGetProjectAPI.emit();
            this.callProjections.emit();
            this.checkValidationStatus.emit();
            this.cdf.detectChanges();
          }
          this.checkedPosition = [];
          this.listSelected = false;
          this.onCancelEditDate();
          this.showPositionUpdateDateDialog = false;
          this.isSubmitting = false;
        },
        (error: HttpErrorResponse) => {
          this.isSubmitting = false;
          this.layoutUtilsService.showActionNotification(error.error.message, AlertType.Error);
        }
      );
    }
  }

  updatePOForMultiPositions() {
    let positionIds = '';
    let reqObj: UpdateMultiPOObj = {} as UpdateMultiPOObj;
    this.checkedPosition.forEach((position) => {
      if (positionIds === '') {
        positionIds += `${position.position.id}`;
      } else {
        positionIds += `,${position.position.id}`;
      }
    });
    reqObj.position_ids = positionIds;
    reqObj.purchase_order_id = this.purchaseOrderIdForMultiPos;
    this.isSubmitting = true;
    this.projectService.updatePOForMultiPositions(reqObj).subscribe(
      (res: OGantryHttpResponse<null>) => {
        if (res.success) {
          this.layoutUtilsService.showActionNotification(this.appConstants.positionDataSave, AlertType.Success);
          this.getPositionList();
          this.positionEdit = false;
          this.callGetProjectAPI.emit();
          this.callProjections.emit();
          this.checkValidationStatus.emit();
          this.cdf.detectChanges();
        }
        this.checkedPosition = [];
        this.listSelected = false;
        this.purchaseOrderIdForMultiPos = null;
        this.showPOUpdateDialog = false;
        this.isSubmitting = false;
      },
      (error: HttpErrorResponse) => {
        this.isSubmitting = false;
        this.layoutUtilsService.showActionNotification(error.error.message, AlertType.Error);
      }
    );
  }

  apiCallForUpdateMultiPos(apiCalls: Observable<OGantryHttpResponse<Position>>[]) {
    forkJoin(apiCalls).subscribe((responses: OGantryHttpResponse<Position>[]) => {
      const allSuccess = responses.every((res) => res['success']);
      if (allSuccess) {
        this.layoutUtilsService.showActionNotification('Data Saved Successfully', AlertType.Success);
        this.getPositionList();
        this.positionEdit = false;
        this.callGetProjectAPI.emit();
        this.callProjections.emit();
        this.checkValidationStatus.emit();
        this.cdf.detectChanges();
      }
      this.checkedPosition = [];
      this.listSelected = false;
      this.purchaseOrderIdForMultiPos = null;
    });
  }

  onCancelUpdatePoForMultiPos() {
    this.showPOUpdateDialog = false;
    this.checkedPosition = [];
    this.listSelected = false;
    this.purchaseOrderIdForMultiPos = null;
  }

  showPODialog(): void {
    this.showPOUpdateDialog = true;
  }

  showPostionDialog() {
    if (!this.dateObj) {
      this.dateObj = { start_date: new Date(), end_date: new Date() };
    }

    if (this.dateObj.start_date && this.dateObj.end_date) {
      this.updatePositionDate.patchValue({
        start_date: new Date(this.dateObj.start_date),
        end_date: new Date(this.dateObj.end_date)
      });
      this.minDateOfProject = this.updatePositionDate.value.end_date ? this.updatePositionDate.value.start_date : new Date(this.dateObj.start_date);
      this.showPositionUpdateDateDialog = true;
    }
  }

  onDateChange(): void {
    this.isValidSelectedDate = this.updatePositionDate.value.end_date ? this.updatePositionDate.value.start_date <= this.updatePositionDate.value.end_date : true;
    this.minDateOfProject = this.updatePositionDate.value.end_date ? this.updatePositionDate.value.start_date : new Date(this.dateObj.start_date);
  }

  onSelectColumnChange(event): void {
    if (event) {
      this.columnToggle.setSelectedColumns(event.value, 'postion');
      this._selectedColumns = event.value;
      this._pCols = event.value.map((f) => f.field);
    }
    this.totalColumns = this._pCols.length + this.noOfColumns;
    this.isShowHideColumns = !this.isShowHideColumns;
    if (this.positionEdit || this.showAddPosition) {
      this.addColumn();
    }
  }

  updatePostionColumn(): void {
    this._selectedColumns = JSON.parse(localStorage.getItem('selectedColumnsArray'))?.postion
      ? JSON.parse(localStorage.getItem('selectedColumnsArray'))['postion']
      : this.frozenCols;

    this.frozenCols = [
      { field: 'cost', monthLabel: 'Hourly Cost' },
      { field: 'expense', monthLabel: 'Expense' },
      { field: 'allocation_daily', monthLabel: 'Allocation (Daily)' },
      { field: 'allocation_weekly', monthLabel: 'Allocation (weekly)' },
      { field: 'purchase_order', monthLabel: 'Purchase Order' }
    ];
    this._pCols = this._selectedColumns?.map((f) => f.field);
  }

  addColumn(): void {
    const extendedField = this.extractExtendFlow();
    const allocationFields = ['allocation_daily', 'allocation_weekly'];
    const selectedAllocations = this._pCols.filter((col) => allocationFields.includes(col));
    let allocationToAdd: any[] = [];
    if (selectedAllocations.length === 1) {
      allocationToAdd = this.frozenCols.filter((col) => allocationFields.includes(col.field) && selectedAllocations.includes(col.field));
    } else if (selectedAllocations.length === 2) {
      allocationToAdd = this.frozenCols.filter((col) => allocationFields.includes(col.field));
    } else {
      allocationToAdd = this.frozenCols.filter((col) => col.field === 'allocation_daily');
    }
    const otherCols = [
      ...this.frozenCols.filter(
        (col) => !allocationFields.includes(col.field) && (this._pCols?.includes(col.field) || !extendedField?.some((column2) => col?.field === column2?.field))
      )
    ];
    this._selectedColumns = [...otherCols, ...allocationToAdd];

    this._pCols = this._selectedColumns?.map((f) => f.field);
    this.columnToggle.setSelectedColumns(this._selectedColumns, 'postion');
  }

  onCancelEditDate(): void {
    this.isValidSelectedDate = true;
    this.updatePositionDate.reset();
    this.showPositionUpdateDateDialog = false;
  }

  isSaveButtonDisable(): boolean {
    return !(this.updatePositionDate?.value?.isUpdateStartDate || this.updatePositionDate?.value?.isUpdateEndDate) || !this.isValidSelectedDate || this.updatePositionDate.invalid;
  }

  getValueByPartialKey(dbTag: string, extendFieldsObj: any = {}): string {
    return extendFieldsObj?.hasOwnProperty(dbTag) ? extendFieldsObj[dbTag] : '';
  }

  checkSelectedColumn(key: string): boolean {
    const finalKey = key?.replace(/\s/g, '_')?.trim() || '';
    return this._pCols.includes(finalKey);
  }

  extractNames(data: any, componentName: string): string[] {
    return data
      ?.map((item) => {
        const projectConfig = item?.jsonData?.extendedFieldsConfig?.find((config) => config?.component === componentName);
        return projectConfig ? projectConfig?.fields.map((field) => field?.name?.trim()) : [];
      })
      .flat(2);
  }

  extractExtendFlow(): Array<any> {
    let extendFiled = [];
    let prepareKey = this.extractNames(this.extendFields, this.componentType);
    for (const key of prepareKey ?? []) {
      extendFiled.push({
        field: key?.replace(/\s/g, '_')?.trim() || '',
        monthLabel: key?.toUpperCase()
      });
    }
    return extendFiled;
  }

  getGlobalDetailsCategory(): void {
    this.subscriptionManager.add(
      this.adminService.getExtendedFields('ManageExtendedFiled').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'ManageExtendedFiled') {
              this.extendFields = res.data.global_details[0].global_detail.extended_fields.extendArray || [];

              this.frozenCols = [...this.frozenCols, ...this.extractExtendFlow()];
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  openExtendFiledPopup(positionObj: Position, filedName: string = ''): void {
    this.updateExtendFiled = filedName.toString();
    this.showUpdateExtendFiledDialog = true;
    this.positionObj = positionObj;
    this.projectId = positionObj?.position?.project?.id;
    this.positionSetupForm = JSON.parse(JSON.stringify(this.positionObj));
    this.editPositionObj = positionObj;
  }

  closeExtendFiledPopup(): void {
    this.position = this.position.map((pos) => {
      if (pos.position?.id === this.positionObj?.position?.id) {
        return {
          position: {
            ...pos.position,
            extended_fields: {
              ...this.positionSetupForm?.position?.extended_fields
            }
          }
        };
      }
      return pos;
    });

    this.updateExtendFiled = '';
    this.showUpdateExtendFiledDialog = false;
    this.positionObj = {};
  }

  ngOnDestroy(): void {
    this.columnToggle.setSelectedColumns(this._selectedColumns, 'postion');
  }

  updateWeeklyBillableHours(dailyHoursInput: string): void {
    const dailyHours = parseFloat(dailyHoursInput) || 0;
    const weeklyHours = (dailyHours * 5).toFixed(2);
    this.weeklyHours = Number(weeklyHours);
  }

  updateDailyBillableHours(weeklyHoursInput: string): void {
    const weeklyHours = parseFloat(weeklyHoursInput) || 0;
    this.weeklyHours = weeklyHours;

    const dailyHours = (this.weeklyHours / 5).toFixed(2);
    this.addPositionForm.get('daily_billable_hours')?.setValue(dailyHours);
    this.addPositionForm.get('daily_billable_hours')?.markAsTouched();
  }

  returnExceptedValue(editPositionObj: any): number {
    return (editPositionObj.position.weekly_billable_hours = editPositionObj?.position?.weekly_billable_hours
      ? editPositionObj?.position?.weekly_billable_hours
      : ((editPositionObj?.position?.daily_billable_hours || 0) * 5).toFixed(2));
  }

  editValueChange(editPositionObj): any {
    editPositionObj.position.daily_billable_hours = editPositionObj.position.weekly_billable_hours ? (editPositionObj.position.weekly_billable_hours / 5).toFixed(2) : 0;
    this.allocationValueChange();
  }

  showAlert(
    {
      actions
    }: {
      actions?: { acceptAction: () => void; rejectAction?: () => void };
    },
    positionCost?: number,
    employeeCost?: number
  ): Observable<boolean> {
    const subject = new Subject<boolean>();

    this.confirmationService.confirm({
      message: `The current cost on this position is $${positionCost}, on the new start date, this employees cost is $${employeeCost}, how would you like to proceed?`,
      header: 'Position cost update',
      icon: 'pi pi-info-circle',
      acceptIcon: 'none',
      rejectIcon: 'none',
      rejectButtonStyleClass: 'p-button-text',
      accept: () => {
        subject.next(true);
        subject.complete();
      },
      reject: () => {
        subject.next(false);
        subject.complete();
      },
      acceptLabel: 'Use updated cost', // Custom accept button label
      rejectLabel: 'Keep current cost',
      key: 'conformationDialog'
    });

    return subject.asObservable();
  }

  openPurchaseOrderSidebar(): void {
    this.purchaseOrderSidebar.emit(true);
  }

  getSymbol(value: string): string {
    return value === 'gte' ? '>=' : value === 'lt' ? '<' : '=';
  }

  getFormattedFilterDate(key: string): string | Date {
    if (key === 'start_date') {
      return this.dataFilter?.start_date ? new Date(this.dataFilter?.start_date).toLocaleDateString() : '';
    } else if (key === 'end_date') {
      return this.dataFilter?.end_date ? new Date(this.dataFilter?.end_date).toLocaleDateString() : '';
    }
  }

  setDateFilter(): void {
    this.dateFilter = [
      {
        label: 'Greater than or equal to',
        value: 'gte'
      },
      {
        label: 'Equal to',
        value: 'eq'
      },
      {
        label: 'Less than',
        value: 'lt'
      }
    ];
  }

  setDataFilterDefault(): void {
    this.dataFilter = {
      name: '',
      employee_id: null,
      position_type_id: null,
      start_date: null,
      end_date: null,
      daily_billable_hours: null,
      cost: null,
      bill_rate: null
    };
  }

  onClearDateFilter(key: string): void {
    this.dataFilter[key] = null;
    this.getPositionList();
  }

  sortColumn(): void {
    this.sortColumnFlag = true;
  }

  onMonthSelect(): void {
    const uniqueMonths: Date[] = [];
    const uniqueMonthsMap = new Map<string, boolean>();
    this.selectedMonths?.forEach((date) => {
      const month = new Date(date).getMonth() + 1;
      const year = new Date(date).getFullYear();
      const key = `${month}-${year}`;
      if (!uniqueMonthsMap.has(key)) {
        uniqueMonthsMap.set(key, true);
        uniqueMonths.push(date);
      }
    });

    if (uniqueMonths.length < (this.selectedMonths?.length || 0)) {
      this.selectedMonths = uniqueMonths;
    }

    this.selectedMonth = this.selectedMonths?.map((date) => ({
      month: new Date(date).getMonth() + 1,
      year: new Date(date).getFullYear()
    }));

    this.cdf.detectChanges();
  }

  isDateSelected(date: any): boolean {
    if (!this.selectedMonths || this.selectedMonths.length === 0) {
      return false;
    }

    return this.selectedMonths.some((selectedDate) => {
      const selectedMonth = new Date(selectedDate).getMonth();
      const selectedYear = new Date(selectedDate).getFullYear();
      return date.month === selectedMonth && date.year === selectedYear;
    });
  }

  getFormattedMonthYear(date: Date): string {
    const monthNames = this.appConstants.MonthCalenderConfig.monthName;
    const month = new Date(date).getMonth();
    const year = new Date(date).getFullYear().toString().slice(-2);
    return `${monthNames[month]}/${year}`;
  }

  removeSelectedMonth(dateToRemove: Date): void {
    const monthToRemove = new Date(dateToRemove).getMonth();
    const yearToRemove = new Date(dateToRemove).getFullYear();

    this.selectedMonths = this.selectedMonths.filter((date) => {
      const month = new Date(date).getMonth();
      const year = new Date(date).getFullYear();
      return !(month === monthToRemove && year === yearToRemove);
    });

    this.onMonthSelect();
  }

  addMultipleExpense(apiCalls: any[], expenseList: any[]): void {
    this.subscriptionManager.add(
      forkJoin(apiCalls).subscribe({
        next: (responses: any[]) => {
          const allSuccess = responses?.every((res) => res?.success);
          if (allSuccess) {
            this.positionExpenseList = expenseList;
            const extractedExpenses = responses.map((res) => res?.data?.position_monthly_expense).filter((expense) => expense);
            extractedExpenses?.forEach((expense) => {
              this.positionExpenseList.push({
                ...expense,
                key: 'Monthly'
              });
            });
            this.onMonthlyExpenseAddedSuccessFully();
          }
        },
        error: () => {
          this.expenseLoader = false;
          this.positionExpenseList = expenseList;
          this.resetExpenseMonthValue();
          this.cdf.detectChanges();
        }
      })
    );
  }

  readyMultipleAPIforAddExpense(addExpenseForm: any, addExpenseService: string): Array<any> {
    if (!this.selectedMonth?.length) {
      return [];
    }

    return this.selectedMonth.map(({ month, year }) => {
      const addExpenseObj = {
        cost: addExpenseForm.cost,
        description: addExpenseForm.description,
        position_id: this.positionId,
        month,
        year,
        type_id: addExpenseForm.type_id
      };

      return this.projectService[addExpenseService](addExpenseObj);
    });
  }

  onMonthlyExpenseAddedSuccessFully(): void {
    this.addExpenseForm.reset();
    this.showDailyExpenseType = false;
    this.allowExpenseSelection = false;
    this.expenseLoader = false;
    this.positionMinDate = null;
    this.callProjections.emit();
    this.checkValidationStatus.emit();
    this.cdf.detectChanges();
    this.resetExpenseMonthValue();
    this.layoutUtilsService.showActionNotification(this.appConstants.expenseAddedSuccess, AlertType.Success);
  }

  resetExpenseMonthValue(): void {
    this.selectedMonths = [];
    this.selectedMonth = [];
  }

  highlightSelectedMonths(event?: any): void {
    const monthButtons = document.querySelectorAll('.p-monthpicker-month');
    const calendarTitle = document.querySelector('.p-datepicker-year')?.textContent;

    if (!calendarTitle) return;

    const yearMatch = calendarTitle.match(this.appConstants.MonthCalenderConfig.calenderTitleMatch);
    if (!yearMatch) return;

    const visibleYear = parseInt(yearMatch[0]);

    monthButtons?.forEach((el, index) => {
      el.classList.remove('selected-month-yellow', 'disabled-month');

      this.selectedMonths?.forEach((date) => {
        if (date.getFullYear() === visibleYear && date.getMonth() === index) {
          el.classList.add('selected-month-yellow');
        }
      });

      const monthDate = new Date(visibleYear, index, 1);

      if (monthDate < new Date(this.minDate.getFullYear(), this.minDate.getMonth(), 1) || monthDate > new Date(this.maxDate.getFullYear(), this.maxDate.getMonth(), 1)) {
        el.classList.add('disabled-month');
      }
    });
  }

  listenChange(): void {
    const nextButton = document.querySelector('.p-datepicker-next');
    if (nextButton) {
      nextButton.addEventListener('click', () => {
        setTimeout(() => {
          this.highlightSelectedMonths();
        }, 0);
      });
    }
  }

  @Debounce(200)
  hideCalendarNavButtons(): void {
    const popups = document.querySelectorAll('.p-datepicker');
    popups.forEach((popup) => {
      if (popup && popup.parentElement && popup.parentElement.contains(this.p_calender.nativeElement)) {
        const nextBtn = popup.querySelector('.p-datepicker-next') as HTMLElement;
        const prevBtn = popup.querySelector('.p-datepicker-prev') as HTMLElement;
        if (nextBtn) nextBtn.style.display = 'none';
        if (prevBtn) prevBtn.style.display = 'none';
      }
    });
  }

  addToExpenseChangedPositionList(position: Position): void {
    const exists = this.isPositionInChangedList(position);
    if (!exists) {
      this.expenseChangedPositions.push({ position });
      localStorage.setItem('changedExpensePosition', JSON.stringify(this.expenseChangedPositions));
    }
  }

  isPositionInChangedList(position: Position): boolean {
    return this.expenseChangedPositions?.some((obj) => obj.position?.position?.id === position.position?.id);
  }

  onFinancialCalculationChanged(newValue: boolean): void {
    if (!newValue) {
      this.expenseChangedPositions = [];
    }
  }

  duplicatePositionBYId(id: number, dt: any): void {
    this.loading$.next(true);
    this.projectService.duplicatePosition(id).subscribe({
      next: (res) => {
        this.layoutUtilsService.showActionNotification(this.appConstants.duplicatePosition, AlertType.Success);
        this.getPositionList();
        this.cdf.detectChanges();
        // Animation for user change
        setTimeout(() => {
          this.editPosition({ position: res.data.position }, dt);
        }, 1000);
      },
      error: () => {
        this.layoutUtilsService.showActionNotification(this.appConstants.duplicatePositionError, AlertType.Error);
      },
      complete: () => {
        this.loading$.next(false);
      }
    });
  }
}
