<span class="benchReportWrapper">
  <mat-drawer-container class="h-100" id="benchReportContainer">
    <mat-drawer class="detail-sidebar" #sidebarFilter mode="over" position="end" disableClose [opened]="openFilter">
      <div class="card card-custom add-contact">
        <app-card-header [cardTitle]="filterCardTitle" [buttons]="filterButtons" [cardLabelClass]="'mb-0'"></app-card-header>
        <div class="card-body create-card">
          <form class="form" autocomplete="off" novalidate="novalidate" id="applyFilter" (ngSubmit)="filterReport()">
            <div class="row">
              <div class="col-12">
                <app-shared-range-selector
                  [showDateRangeRadioButton]="dataFilter.start_month && dataFilter.end_month ? true : false"
                  [rollingRadioBtnSelected]="dataFilter.rollingOption ? true : false"
                  [yearRadioBtnSelected]="dataFilter.year ? true : false"
                  [quaterRadioBtnSelected]="dataFilter.quarter ? true : false"
                  [start_month]="dataFilter.start_month"
                  [end_month]="dataFilter.end_month"
                  [selectedYear]="dataFilter.year"
                  [selectedQuarterValue]="dataFilter.quarter"
                  [selectedRollingOption]="dataFilter.rollingOption"
                  (selected_end_date)="endMonthSelected($event)"
                  (selected_start_date)="startMonthSelected($event)"
                  (updatedYearValue)="dataFilter.year = $event"
                  (updatedRollingValue)="dataFilter.rollingOption = $event"
                  (updatedquaterValue)="dataFilter.quarter = $event"
                  [dateError]="dateError"
                  [dateRequired]="dateRequired"
                ></app-shared-range-selector>
                <div class="PL-border">
                  <div class="form-group" id="project-status">
                    <label class="form-label background">Project Status</label>
                  </div>
                  <div class="form-group first dropdown p-2">
                    <p-multiSelect
                      #multiSelectComp
                      [(ngModel)]="projectStatus"
                      [overlayVisible]="true"
                      [showHeader]="false"
                      [options]="statuses"
                      [ngModelOptions]="{ standalone: true }"
                      placeholder="Status"
                      display="chip"
                      (onChange)="statusSelected($event)"
                    >
                    </p-multiSelect>
                  </div>
                </div>
                <div class="PL-border">
                  <div class="form-group">
                    <label class="form-label background">
                      <div class="width-65">
                        {{ showEmployeeFilter ? 'Saved Employee Filter' : 'Employee' }}
                      </div>
                      <div class="form-group pt-0 save-filter-radio">
                        <div class="form-check form-check-inline mt-2" id="saved-employee">
                          <input
                            class="form-check-input"
                            type="radio"
                            id="saveEmployeeFilter"
                            [checked]="showEmployeeFilter"
                            autocomplete="off"
                            (click)="showSaveEmployeeFilterSelected()"
                          />
                          <label class="mb-0" for="saveEmployeeFilter"> Use Saved Employee Filter </label>
                        </div>
                      </div>
                    </label>
                  </div>
                  <div class="form-group first dropdown p-2" *ngIf="showEmployeeFilter">
                    <p-dropdown
                      appendTo="body"
                      placeholder="Select"
                      [(ngModel)]="dataFilter.name"
                      [options]="employeeGroup"
                      [ngModelOptions]="{ standalone: true }"
                      (ngModelChange)="getEmployeeIds()"
                    >
                    </p-dropdown>
                  </div>
                  <div class="form-group first dropdown p-2" *ngIf="!showEmployeeFilter">
                    <p-multiSelect
                      appendTo="body"
                      #multiSelectComp2
                      [(ngModel)]="employeeName"
                      [overlayVisible]="false"
                      [showHeader]="false"
                      [options]="employeeList"
                      [ngModelOptions]="{ standalone: true }"
                      placeholder="Select"
                      display="chip"
                      [disabled]="!employeeList.length"
                      (onChange)="employeeSelected($event)"
                    >
                    </p-multiSelect>
                  </div>
                  <div class="form-group first dropdown p-2 pl-0 pr-0" *ngIf="!showEmployeeFilter">
                    <p-dropdown
                      appendTo="body"
                      placeholder="Select"
                      [(ngModel)]="dataFilter.employee_status"
                      [options]="employeeStatusList"
                      [ngModelOptions]="{ standalone: true }"
                      (onChange)="getEmployeeList()"
                    >
                    </p-dropdown>
                  </div>
                </div>

                <div class="PL-border">
                  <div class="form-group">
                    <label class="form-label background">Type</label>
                  </div>
                  <div class="form-group first dropdown p-2">
                    <p-dropdown
                      appendTo="body"
                      [(ngModel)]="dataFilter.employee_type_name"
                      [ngModelOptions]="{ standalone: true }"
                      [options]="employeeTypes"
                      placeholder="Select"
                      optionLabel="employee_type.name"
                      optionValue="employee_type.name"
                      showClear="true"
                    ></p-dropdown>
                  </div>
                </div>
                <div class="PL-border">
                  <div class="form-group">
                    <label class="form-label background">Tags</label>
                  </div>
                  <div class="form-group first dropdown p-2">
                    <p-treeSelect
                      [(ngModel)]="selectedTags"
                      (ngModelChange)="tagSelected($event)"
                      class="filter-tags"
                      [options]="groupedCategory?.data"
                      display="chip"
                      [metaKeySelection]="false"
                      selectionMode="checkbox"
                      placeholder="Select Tags"
                      [ngModelOptions]="{ standalone: true }"
                      filterBy="label"
                    >
                    </p-treeSelect>
                  </div>
                </div>
                <div class="PL-border">
                  <div class="form-group">
                    <label class="form-label background">Results Format</label>
                  </div>
                  <div class="form-group first dropdown p-2">
                    <p-dropdown appendTo="body" placeholder="Select" [(ngModel)]="selectedUtiliuzation" [options]="viewUtilizationFormates" [ngModelOptions]="{ standalone: true }">
                    </p-dropdown>
                  </div>
                </div>
                <div class="PL-border allocation-wrapper">
                  <div class="form-group">
                    <label class="form-label background">Allocation Threshold</label>
                  </div>
                  <div class="form-group first dropdown p-2">
                    <p-multiSelect
                      [options]="allocationOptions"
                      [(ngModel)]="dataFilter.selectedAllocationType"
                      placeholder="Select Allocation"
                      [showHeader]="false"
                      [ngModelOptions]="{ standalone: true }"
                    >
                      <ng-template let-option pTemplate="selectedItem">
                        {{ option.label }}
                      </ng-template>
                      <ng-template let-option pTemplate="item">
                        <div class="input-container">
                          {{ option.label }}
                          <input
                            class="form-control"
                            *ngIf="option.value === 'Over Allocated'"
                            type="number"
                            [(ngModel)]="dataFilter.selectedoverAllocationValue"
                            placeholder="Over Allocated Value"
                            [ngModelOptions]="{ standalone: true }"
                            (click)="$event.stopPropagation()"
                            (keyup)="validateSecondInput(dataFilter.selectedoverAllocationValue)"
                          />
                          <input
                            class="form-control"
                            *ngIf="option.value === 'Under Allocated'"
                            type="number"
                            [(ngModel)]="dataFilter.selectedunderAllocationValue"
                            placeholder="Under Allocated Value"
                            [ngModelOptions]="{ standalone: true }"
                            (click)="$event.stopPropagation()"
                            (keyup)="validateSecondInput(dataFilter.selectedunderAllocationValue)"
                          />
                        </div>
                      </ng-template>
                    </p-multiSelect>
                  </div>
                </div>
                <div class="d-flex justify-content-around">
                  <div>
                    <input class="form-check-input" type="radio" id="calendarView" [checked]="defaultView === 'calendar'" (click)="setDefaultView('calendar')" />
                    <label class="mb-0" for="calendarView"> Defaults to Calendar view </label>
                  </div>
                  <div>
                    <input class="form-check-input" type="radio" id="tableView" [checked]="defaultView === 'table'" (click)="setDefaultView('table')" />
                    <label class="mb-0" for="tableView"> Defaults to Table View </label>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-group d-flex flex-wrap justify-content-end align-items-center mt-2 pb-4">
              <button id="addContactCancel" type="button" class="btn-cancel" (click)="clearFilters()">Reset</button>
              <button id="addContactSubmit" type="submit" [isSubmitting]="isSubmitting" class="btn-save">Apply</button>
            </div>
          </form>
        </div>
      </div>
    </mat-drawer>
    <mat-drawer-content class="detail-sidebar-content">
      <ng-container *ngTemplateOutlet="benchReport"></ng-container>
    </mat-drawer-content>
  </mat-drawer-container>
  <ng-template #benchReport>
    <div class="card card-custom">
      <app-card-header
        [cardTitle]="cardTitle"
        [cardSubTitle]="cardSubTitle"
        [buttons]="buttons"
        [showSplitButton]="true"
        [splitButtonDropDownOption]="splitButtonDropDownOption"
      ></app-card-header>
      <div class="card-body table" [hidden]="!tableButtonActive">
        <div class="d-flex justify-content-between align-items-start gap-5">
          <div>
            <app-selected-filter-tags
              (filterReset)="resetFilter()"
              (saveFilter)="onSaveFilter()"
              [tags]="tags"
              (onCancel)="onCloseFromFilterPanel($event)"
              (onRemoveStatus)="onRemoveStatusFilter($event)"
            ></app-selected-filter-tags>
          </div>
          <div class="d-flex flex-nowrap align-items-center">
            <div (clickOutside)="isShowHideColumns ? (isShowHideColumns = false) : ''">
              <div class="float-right mr-2 py-2 pointer" (click)="isShowHideColumns = !isShowHideColumns" #coulmnToggel>
                <app-filter-icon-shared></app-filter-icon-shared>
              </div>
              <span class="bench">
                <div class="popup-column card">
                  <app-filter-table-fields
                    *ngIf="isShowHideColumns"
                    [selectedColumns]="selectedColumns"
                    [frozenCols]="frozenCols"
                    dynamicBindingKey="monthLabel"
                    (onSelectColumChange)="onSelectColumsChange($event)"
                  ></app-filter-table-fields>
                </div>
              </span>
            </div>
            <div class="calendar-view calendar-filter-panel">
              <div class="calendar-filter-body justify-content-end w-100">
                <div>
                  <span class="p-buttonset d-flex align-items-center right-align mx-2 my-2 justify-content-end mr-4">
                    <ng-container *ngIf="loading && !resizeFlag">
                      <div class="custome-p-table-spinner">
                        <em class="pi pi-spin pi-spinner" style="font-size: 2rem"></em>
                      </div>
                    </ng-container>
                    <ng-container *ngFor="let button of buttons">
                      <ng-container *ngIf="button?.isSwitcherButton">
                        <button
                          [type]="button?.btnType || 'button'"
                          [class]="button?.btnClass"
                          [ngClass]="{
                            'f-s-20': button?.btnSvg,
                            'switch-active': button?.isActive
                          }"
                          [isSubmitting]="button?.loading"
                          (click)="onAction(button)"
                        >
                          <ng-container *hasAnyPermission="button?.permissions || []; disableEvent: true">
                            <ng-container *ngIf="button?.btnText">{{ button?.btnText }}</ng-container>
                            <ng-container *ngIf="button?.btnSvg">
                              <a title="Filter" class="btn btn-icon btn-sm icon-background">
                                <span [inlineSVG]="'assets/media/svg/icons/' + button?.btnSvg + '.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
                              </a>
                            </ng-container>
                            <ng-container *ngIf="button?.btnIcon">
                              <fa-icon [icon]="button?.btnIcon"></fa-icon>
                            </ng-container>
                          </ng-container>
                        </button>
                      </ng-container>
                    </ng-container>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="scrollable-content bench-report-wrapper"
          *ngIf="!resizeFlag; else resize_table"
          [ngClass]="benchReportData.length !== 0 && benchReportData.length <= 22 ? 'active' : ''"
        >
          <ng-container *ngIf="showApplyMsg">
            <p class="filter-note ml-5 apply-filter-msg">Please Apply Filter To Load The Utilization Report.</p>
          </ng-container>
          <p-table
            scrollDirection="both"
            [resizableColumns]="true"
            *isFetchingData="loading$"
            [columns]="tableHeaders"
            [scrollable]="true"
            [scrollHeight]="height"
            #benchReportTable
            [value]="benchReportData"
            (sortFunction)="customSort($event)"
            [customSort]="true"
            dataKey="id"
            [sortField]="sortFieldName"
            [sortOrder]="sortOrderNumber"
          >
            <ng-template pTemplate="header" let-columns>
              <tr></tr>
              <tr class="p-table-th-wrapper">
                <ng-container *ngFor="let col of frozenCols; let i = index">
                  <ng-container *ngIf="_pCols?.includes(col.field)">
                    <th
                      [ngClass]="col.cssClass ? col.cssClass.toString() : getFixCssClass(columns)"
                      *ngIf="_pCols?.includes(col.field)"
                      [pSortableColumn]="col?.field"
                      [pSortableColumnDisabled]="!col.sort"
                      pFrozenColumn
                      [frozen]="col?.isFrozenColumn"
                      pResizableColumn
                    >
                      {{ col.monthLabel }}
                      <span *ngIf="col?.sort">
                        <p-sortIcon [field]="col?.field"></p-sortIcon>
                      </span>
                    </th>
                  </ng-container>
                </ng-container>
                <th
                  *ngFor="let col of columns"
                  class="text-number-right"
                  [ngClass]="getDynCssClass(columns)"
                  [pSortableColumn]="col?.field"
                  [pSortableColumnDisabled]="!col.sort"
                  pResizableColumn
                >
                  {{ col.monthLabel }}
                  <span *ngIf="col?.sort">
                    <p-sortIcon [field]="col?.field"></p-sortIcon>
                  </span>
                </th>
                <th id="actions" class="header-width-action pt-0"></th>
              </tr>
            </ng-template>

            <ng-template pTemplate="body" let-data let-columns="columns">
              <tr class="p-table-td-wrapper">
                <ng-container *ngFor="let col of frozenCols">
                  <td
                    [ngClass]="getFixCssClass(columns)"
                    pFrozenColumn
                    *ngIf="col?.field === 'first_name' && _pCols?.includes('first_name')"
                    id="showData"
                    (clickOutside)="data.showHelpIconData = false"
                  >
                    <div class="d-flex">
                      <div class="ellipses" [title]="data?.employee?.first_name + ' ' + data?.employee?.last_name">
                        {{ data?.employee?.first_name }}
                        {{ data?.employee?.last_name }}
                      </div>

                      <ng-template #popOver>
                        <div *isFetchingData="loading$$">
                          <div [ngStyle]="styleObj.heading">CONTACT INFO</div>
                          <div [ngStyle]="styleObj.subHeading">
                            {{ data?.employee?.email || '-' }}
                          </div>
                          <div [ngStyle]="styleObj.heading">EMPLOYEE ROLE</div>
                          <div [ngStyle]="styleObj.subHeading">
                            {{ data?.employee?.role || '-' }}
                          </div>
                          <div [ngStyle]="styleObj.heading">EMPLOYEE TYPE</div>
                          <div [ngStyle]="styleObj.subHeading">
                            {{ data?.employee?.employee_type?.name }}
                          </div>
                        </div>
                      </ng-template>
                      <div>
                        <a placement="right" container="body" (click)="showHelpData(data)" [ngbPopover]="popOver">
                          <fa-icon [icon]="'info-circle'" class="ml-1 help-icon"></fa-icon>
                        </a>
                      </div>
                    </div>
                  </td>
                  <td *ngIf="col?.field === 'skill_set' && _pCols?.includes('skill_set')" class="skill-set-wrapper">
                    <div class="ellipses" [title]="getSkillSet(data?.employee?.position_types)">
                      {{ getSkillSet(data?.employee?.position_types) }}
                    </div>
                  </td>
                  <td *ngIf="col?.field === 'employee_type_name' && _pCols?.includes('employee_type_name')" class="ellipses employee-type-wrapper">
                    {{ data?.employee?.employee_type?.name }}
                  </td>
                  <td class="ellipses" [ngClass]="'tags-wrapper'" *ngIf="col?.field === 'tags' && _pCols?.includes('tags')">
                    <ng-container *ngIf="data?.employee?.tags.length">
                      <span class="ellipses">
                        <span class="taglist">
                          <p-chip *ngFor="let tag of data?.employee?.tags; let i = index" (click)="openTagModal(data?.employee?.tags)" class="cursor-pointer">
                            <span class="tooltip-hover" container="body" [ngbTooltip]="categoryDetails" #t4="ngbTooltip" (mouseenter)="toggleWithCategory(t4, tag)">{{
                              getTagsCount(tag, i < 2)
                            }}</span>
                          </p-chip>
                        </span>
                      </span>
                      <span class="count cursor-pointer" *ngIf="data?.employee?.tags.length > 2" (click)="openTagModal(data?.employee?.tags)">
                        <span class="tag-count">
                          <p-badge [value]="getTagCount(data?.employee?.tags)"></p-badge>
                        </span>
                      </span>
                    </ng-container>
                  </td>
                </ng-container>
                <ng-container *ngFor="let col of columns">
                  <td class="text-number-right" [ngClass]="getDynCssClass(columns)" *ngIf="col?.id">
                    <div
                      class="cursor-pointer"
                      title="Click me for position details"
                      container="body"
                      (click)="showPositionData(popOver, data, col, columns)"
                      [ngbPopover]="popOverContent"
                      popoverClass="custom-pop"
                      #popOver="ngbPopover"
                      triggers="manual"
                    >
                      {{ getValues(col, data) }}
                    </div>
                    <ng-template #popOverContent>
                      <div class="d-block text-right">
                        <button class="btn-close-icon" (click)="closePopOver(popOver)">
                          <fa-icon icon="times"></fa-icon>
                        </button>
                      </div>
                      <p-table responsiveLayout="scroll" #dt [value]="activeEmployee" dataKey="id" [loading]="loadingEmp" styleClass="p-datatable-customers">
                        <ng-template pTemplate="header">
                          <tr>
                            <th id="company" class="header-width">Client</th>
                            <th id="contactPerson" class="header-width">Project</th>
                            <th id="phone" class="header-width">Start Date</th>
                            <th id="email" class="header-width">End Date</th>
                            <th class="header-width">Allocation</th>
                            <th id="email" class="header-width">Project Status</th>
                          </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-customer>
                          <tr>
                            <td>
                              {{ customer?.customer?.project?.customer?.name }}
                            </td>
                            <td>
                              <a
                                class="ellipses"
                                *hasAnyPermission="permissionModules.MANAGE_PROJECT; disableEvent: true"
                                [routerLink]="[appRoutes.EDIT_PROJECT, customer?.project?.id]"
                              >
                                <span [title]="customer.project.name">
                                  {{ customer.project.name }}
                                </span>
                              </a>
                            </td>
                            <td>
                              {{ customer.start_date | date : 'MM/dd/yyyy' }}
                            </td>
                            <td>
                              {{ customer.end_date | date : 'MM/dd/yyyy' }}
                            </td>
                            <td>
                              {{ customer.allocation }}
                            </td>
                            <td>
                              <span [title]="customer?.customer?.project?.status">
                                {{ customer?.customer?.project?.status }}
                              </span>
                            </td>
                          </tr>
                        </ng-template>
                        <ng-template pTemplate="emptymessage">
                          <tr>
                            <td colspan="6" class="text-center">No Data found.</td>
                          </tr>
                        </ng-template>
                      </p-table>
                    </ng-template>
                  </td>
                </ng-container>
                <td class="header-width-action"></td>
              </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
              <tr *ngIf="!showApplyMsg && benchReportData.length === 0 && !loading">
                <td colspan="2" class="center-align">No Data found.</td>
              </tr>
            </ng-template>
          </p-table>
        </div>
        <ng-template #categoryDetails let-tag="tag">
          <p [innerHTML]="getTagCategorySubCategory(tag)"></p>
        </ng-template>

        <p-dialog header="Applied Tags" [(visible)]="showTagDialog" [modal]="true" class="dialog-applied-tags" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
          <ul>
            <li *ngFor="let tag of selectedTagToView">
              <span [ngbTooltip]="categoryDetails" #t3="ngbTooltip" (mouseenter)="toggleWithCategory(t3, tag)">{{ getExtractedTags(tag) }}</span>
            </li>
          </ul>
        </p-dialog>
        <ng-template #resize_table>
          <div class="scrollable-content bench-report-wrapper">
            <ng-container *ngIf="!(loading$ | async) && !loading && benchReportData.length === 0">
              <p class="filter-note ml-5 apply-filter-msg">Please Apply Filter To Load The Utilization Report.</p>
            </ng-container>
            <p-table
              [columns]="tableHeaders"
              *isFetchingData="loading$"
              [scrollable]="true"
              [scrollHeight]="height"
              scrollDirection="both"
              #benchReportTable
              [value]="benchReportData"
              [customSort]="true"
              [sortField]="sortFieldName"
              [sortOrder]="sortOrderNumber"
              dataKey="id"
              [loading]="loading"
            >
              <ng-template pTemplate="header" let-columns>
                <tr></tr>
                <tr>
                  <th *ngFor="let col of frozenCols" style="width: 130px" [pSortableColumn]="col?.field" [pSortableColumnDisabled]="!col.sort">
                    {{ col.monthLabel }}
                    <span *ngIf="col?.sort">
                      <p-sortIcon [field]="col?.field"></p-sortIcon>
                    </span>
                  </th>
                  <th *ngFor="let col of columns" style="width: 130px" [pSortableColumn]="col?.field" [pSortableColumnDisabled]="!col.sort" class="text-right">
                    {{ col.monthLabel }}
                    <span *ngIf="col?.sort">
                      <p-sortIcon [field]="col?.field"></p-sortIcon>
                    </span>
                  </th>
                </tr>
              </ng-template>
              <ng-template pTemplate="body" let-data let-columns="columns">
                <tr>
                  <ng-container *ngFor="let col of frozenCols">
                    <td style="width: 130px" *ngIf="col?.field === 'first_name'" id="showData" (clickOutside)="data.showHelpIconData = false">
                      <div class="d-flex">
                        <div class="ellipses" [title]="data?.employee?.first_name + ' ' + data?.employee?.last_name">
                          {{ data?.employee?.first_name }}
                          {{ data?.employee?.last_name }}
                        </div>
                        <ng-template #popOver>
                          <div *isFetchingData="loading$$">
                            <div [ngStyle]="styleObj.heading">CONTACT INFO</div>
                            <div [ngStyle]="styleObj.subHeading">
                              {{ data?.employee?.email || '-' }}
                            </div>
                            <div [ngStyle]="styleObj.heading">EMPLOYEE ROLE</div>
                            <div [ngStyle]="styleObj.subHeading">
                              {{ data?.employee?.role || '-' }}
                            </div>
                            <div [ngStyle]="styleObj.heading">EMPLOYEE TYPE</div>
                            <div [ngStyle]="styleObj.subHeading">
                              {{ data?.employee?.employee_type?.name }}
                            </div>
                          </div>
                        </ng-template>
                        <div>
                          <a placement="right" container="body" (click)="showHelpData(data)" [ngbPopover]="popOver">
                            <fa-icon [icon]="'info-circle'" class="ml-1 help-icon"></fa-icon>
                          </a>
                        </div>
                      </div>
                    </td>
                    <td style="width: 130px" *ngIf="col?.field === 'skill_set'">
                      <div class="ellipses" [title]="getSkillSet(data?.employee?.position_types)">
                        {{ getSkillSet(data?.employee?.position_types) }}
                      </div>
                    </td>
                    <td style="width: 130px" *ngIf="col?.field === 'employee_type_name'" class="ellipses">
                      {{ data?.employee?.employee_type?.name }}
                    </td>
                    <td class="ellipses" [ngClass]="getFixCssClass(columns)" *ngIf="col?.field === 'tags' && _pCols?.includes('tags')">
                      <ng-container *ngIf="data?.employee?.tags.length">
                        <span class="ellipses">
                          <span class="taglist">
                            <p-chip *ngFor="let tag of data?.employee?.tags; let i = index" (click)="openTagModal(data?.employee?.tags)" class="cursor-pointer">
                              <span class="tooltip-hover" container="body" [ngbTooltip]="categoryDetails" #t2="ngbTooltip" (mouseenter)="toggleWithCategory(t2, tag)">{{
                                getTagsCount(tag, i < 2)
                              }}</span>
                            </p-chip>
                          </span>
                        </span>
                        <span class="count cursor-pointer" *ngIf="data?.employee?.tags.length > 2" (click)="openTagModal(data?.employee?.tags)">
                          <span class="tag-count">
                            <p-badge [value]="getTagCount(data?.employee?.tags)"></p-badge>
                          </span>
                        </span>
                      </ng-container>
                    </td>
                  </ng-container>
                  <ng-container *ngFor="let col of columns">
                    <td style="width: 130px" *ngIf="col?.id" class="text-right">
                      <div
                        class="cursor-pointer"
                        title="Click me for position details"
                        container="body"
                        (click)="showPositionData(popOver, data, col, columns)"
                        [ngbPopover]="popOverContent"
                        #popOver="ngbPopover"
                        popoverClass="custom-pop"
                        triggers="manual"
                      >
                        {{ getValues(col, data) }}
                      </div>
                      <ng-template #popOverContent> </ng-template>
                    </td>
                  </ng-container>
                </tr>
              </ng-template>
              <ng-template pTemplate="emptymessage">
                <tr *ngIf="!showApplyMsg && benchReportData.length === 0 && !loading">
                  <td colspan="2" class="center-align">No Data found.</td>
                </tr>
              </ng-template>
            </p-table>
          </div>
        </ng-template>
      </div>
      <div class="calendar-view-wrapper" [hidden]="!calendarButtonActive">
        <div class="d-flex gap-5 justify-content-between align-items-start">
          <app-selected-filter-tags
            (filterReset)="resetFilter()"
            (saveFilter)="onSaveFilter()"
            [tags]="tags"
            (onCancel)="onCloseFromFilterPanel($event)"
            (onRemoveStatus)="onRemoveStatusFilter($event)"
          ></app-selected-filter-tags>
          <div class="d-flex flex-nowrap align-items-center">
            <div class="calendar-view calendar-filter-panel">
              <div class="calendar-filter-body" style="width: 100%">
                <div class="w-50">
                  <span class="p-buttonset d-flex align-items-center right-align mx-2 my-2 mr-4 justify-content-end">
                    <ng-container *ngFor="let button of buttons">
                      <ng-container *ngIf="button?.isSwitcherButton">
                        <button
                          [type]="button?.btnType || 'button'"
                          [class]="button?.btnClass"
                          [ngClass]="{
                            'f-s-20': button?.btnSvg,
                            'switch-active': button?.isActive
                          }"
                          (click)="onAction(button)"
                          [isSubmitting]="button?.loading"
                        >
                          <ng-container *hasAnyPermission="button?.permissions || []; disableEvent: true">
                            <ng-container *ngIf="button?.btnText">{{ button?.btnText }}</ng-container>
                            <ng-container *ngIf="button?.btnSvg">
                              <a title="Filter" class="btn btn-icon btn-sm icon-background">
                                <span [inlineSVG]="'assets/media/svg/icons/' + button?.btnSvg + '.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
                              </a>
                            </ng-container>
                            <ng-container *ngIf="button?.btnIcon">
                              <fa-icon [icon]="button?.btnIcon"></fa-icon>
                            </ng-container>
                          </ng-container>
                        </button>
                      </ng-container>
                    </ng-container>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <p-progressSpinner
          *ngIf="loadingReport"
          styleClass="custom-spinner"
          [style]="{ width: '50px', height: '50px' }"
          strokeWidth="5"
          animationDuration="0.7s"
        ></p-progressSpinner>
        <ng-container>
          <mbsc-eventcalendar
            class="md-resource-header-template"
            *ngIf="!preparingCalendarData"
            [view]="myView"
            [options]="calendarOptions"
            [data]="myEvents"
            [scheduleEventTemplate]="eventTemplate"
            [resources]="myResources"
            [resourceTemplate]="resourceTemp"
            [resourceHeaderTemplate]="headerTemp"
          >
            <ng-template #resourceTemp let-resource>
              <div class="md-resource-header-template-cont">
                <div class="md-resource-header-template-seats" style="font-size: 14px; font-weight: normal">
                  {{ resource.name }}
                </div>
                <div class="md-resource-header-template-name" style="font-size: 14px; font-weight: normal">
                  {{ resource.customer }}
                </div>
              </div>
            </ng-template>
            <ng-template #headerTemp>
              <div class="md-resource-header-template-title">
                <div class="md-resource-header-template-seats">First Name</div>
                <div class="md-resource-header-template-name">Last Name</div>
              </div>
            </ng-template>
          </mbsc-eventcalendar>
        </ng-container>
      </div>

      <mbsc-popup class="md-tooltip" #popup [options]="popupOptions">
        <div class="md-tooltip-header d-block text-right">
          <button class="btn-close-icon">
            <fa-icon icon="times" (click)="popup.close()"></fa-icon>
          </button>
        </div>
        <p-table responsiveLayout="scroll" [value]="selectedPosition" dataKey="id" styleClass="p-datatable-customers" class="calender-popup">
          <ng-template pTemplate="header">
            <tr>
              <th id="calender-popup-client" class="header-width">Client</th>
              <th id="calender-popup-project" class="contact-person-header-width">Project</th>
              <th id="calender-popup-start-date" class="header-width">Start Date</th>
              <th id="calender-popup-end-date" class="header-width">End Date</th>
              <th id="calender-popup-status" class="header-width">Project Status</th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-customer class="md-tooltip-info">
            <tr class="md-tooltip-info">
              <td>
                {{ customer?.project?.customer?.name }}
              </td>
              <td>
                <a
                  class="project-name-wrapper"
                  *hasAnyPermission="permissionModules.MANAGE_PROJECT; disableEvent: true"
                  [routerLink]="[appRoutes.EDIT_PROJECT, customer?.project?.id]"
                >
                  <span [title]="customer.project.name">
                    {{ customer.project.name }}
                  </span>
                </a>
              </td>
              <td>{{ customer.start_date | date : 'MM/dd/yyyy' }}</td>
              <td>{{ customer.end_date | date : 'MM/dd/yyyy' }}</td>
              <td>
                <span [title]="customer?.project?.status">
                  {{ customer?.project?.status }}
                </span>
              </td>
            </tr>
          </ng-template>
          <ng-template pTemplate="emptymessage">
            <tr>
              <td colspan="6" class="text-center">No Data found.</td>
            </tr>
          </ng-template>
        </p-table>
      </mbsc-popup>
    </div>
  </ng-template>

  <ng-template #eventTemplate let-data>
    <div class="md-timeline-template-event" [ngStyle]="{ borderColor: data.color, background: data.color }">
      <div class="md-timeline-template-event-cont">
        <span class="md-timeline-template-title" style="color: #000; display: block; font-size: 20px"> {{ data?.title }}</span>
      </div>
    </div>
  </ng-template>

  <p-dialog
    dismissableMask="true"
    backdrop="false"
    position="top-right"
    [(visible)]="showFilterListDialog"
    [modal]="true"
    class="filter-dialog"
    [draggable]="false"
    [resizable]="false"
  >
    <ng-template pTemplate>
      <div class="filter-listing" *ngIf="showSavedFilter">
        <input type="search" class="form-control mb-2" placeholder="Search" (input)="searchFilters($event.target.value)" />
        <div *hasAnyPermission="permissionModules.VIEW_SHARE_FILTER; hideTemplate: true" class="title">Shared Filters</div>
        <ng-container *hasAnyPermission="permissionModules.VIEW_SHARE_FILTER; hideTemplate: true">
          <span *ngIf="sharedFilters?.length; else noData">
            <div
              class="form-check filter-body"
              *ngFor="let filterOption of sharedFilters"
              [ngClass]="{
                'selected-filter': filterOption?.query_filter?.id === selectedFilterFormControl?.value?.query_filter?.id
              }"
            >
              <label class="form-check-label">
                <input
                  [formControl]="selectedFilterFormControl"
                  (ngModelChange)="applySelectedFilterAndUpdateUrl()"
                  type="radio"
                  class="form-check-input custom-radio"
                  [value]="filterOption"
                  name="filteroption"
                />{{ filterOption?.query_filter?.name }}
              </label>
              <div class="filter-icons">
                <ng-container *hasAnyPermission="permissionModules.MANAGE_SHARE_FILTER; hideTemplate: true">
                  <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary copy-icon" (click)="copyLinkToTheFilter(filterOption?.query_filter?.id)">
                    <em title="Copy Filter Link" class="fa-regular fa-copy"></em>
                  </a>
                  <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary share-icon" (click)="unShareFilter(filterOption)">
                    <em class="fa-solid fa-share" title="UnShare Filter"></em>
                  </a>
                  <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
                    <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="editFilter(filterOption)"> </span>
                  </a>
                  <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
                    <span title="Delete" [inlineSVG]="'assets/media/svg/icons/delete.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="deleteFilter(filterOption)">
                    </span>
                  </a>
                </ng-container>
              </div>
            </div>
          </span>
        </ng-container>

        <ng-template #noData>
          <div>-</div>
        </ng-template>
        <div class="title">My Filters</div>
        <span *ngIf="myFilters?.length; else noData">
          <div
            class="form-check filter-body"
            *ngFor="let filterOption of myFilters"
            [ngClass]="{
              'selected-filter': filterOption?.query_filter?.id === selectedFilterFormControl?.value?.query_filter?.id
            }"
          >
            <label class="form-check-label">
              <input
                [formControl]="selectedFilterFormControl"
                (ngModelChange)="applySelectedFilterAndUpdateUrl()"
                type="radio"
                class="form-check-input custom-radio"
                [value]="filterOption"
                name="filteroption"
              />{{ filterOption?.query_filter?.name }}
            </label>
            <div class="filter-icons">
              <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary copy-icon" (click)="copyLinkToTheFilter(filterOption?.query_filter?.id)">
                <em title="Copy Filter Link" class="fa-regular fa-copy"></em>
              </a>
              <a
                class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary"
                (click)="shareFilter(filterOption)"
                *hasAnyPermission="permissionModules.MANAGE_SHARE_FILTER; hideTemplate: true"
              >
                <em class="fa-solid fa-share" title="Share Filter"></em>
              </a>
              <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
                <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="editFilter(filterOption)"> </span>
              </a>
              <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
                <span title="Delete" [inlineSVG]="'assets/media/svg/icons/delete.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="deleteFilter(filterOption)"> </span>
              </a>
            </div>
          </div>
        </span>
      </div>
    </ng-template>
  </p-dialog>

  <p-dialog
    dismissableMask="true"
    backdrop="false"
    position="top-right"
    [(visible)]="showExportOptionDialog"
    [modal]="true"
    class="export-dialog"
    [draggable]="false"
    [resizable]="false"
  >
    <ng-template pTemplate>
      <div class="export-action-listing" *ngIf="showExportOptions">
        <button pButton class="btn p-button-text mb-2 mt-2" icon="pi pi-file-o" iconPos="left" (click)="exportReport('csv')">Export CSV</button>
        <button pButton class="btn p-button-text mb-2" icon="pi pi-file-pdf" iconPos="left" (click)="exportReport('pdf')">Export PDF</button>
        <button pButton class="btn p-button-text mb-2" icon="pi pi-file-excel" iconPos="left" (click)="exportReport('excel')">Export Excel</button>
      </div>
    </ng-template>
  </p-dialog>

  <p-dialog header="Rename" [(visible)]="showEditDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
    <div class="form-group first" *ngIf="editFilterObj">
      <input type="text" pInputText class="form-control custom" [(ngModel)]="editFilterObj.query_filter.name" (input)="inputFilterName()" />
      <small *ngIf="showNameError" class="form-text text-danger"> Name is required</small>
    </div>
    <ng-template pTemplate="footer">
      <div class="d-flex flex-wrap justify-content-end align-items-center">
        <button type="button" class="btn-cancel" (click)="closeModal()">Cancel</button>
        <button type="button" class="btn-save" (click)="saveEditFilter()" [isSubmitting]="isSubmitting">Update</button>
      </div>
    </ng-template>
  </p-dialog>

  <p-dialog header="Delete" [(visible)]="showDeleteDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
    <h5 class="p-m-0 delete-title">Do you want to delete "{{ deleteFilterObj?.query_filter?.name }}" Filter?</h5>
    <ng-template pTemplate="footer">
      <div class="d-flex flex-wrap justify-content-end align-items-center">
        <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
        <button type="button" class="btn-save" (click)="saveDeleteFilter()" [isSubmitting]="isSubmitting">Yes</button>
      </div>
    </ng-template>
  </p-dialog>

  <p-dialog [header]="shareFilterObj?.header" [(visible)]="showShareDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
    <h5 class="p-m-0 delete-title">{{ shareFilterObj?.text }}</h5>
    <ng-template pTemplate="footer">
      <div class="d-flex flex-wrap justify-content-end align-items-center">
        <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
        <button type="button" class="btn-save" (click)="saveShareFilter()" [isSubmitting]="isSubmitting">Yes</button>
      </div>
    </ng-template>
  </p-dialog>
</span>

<p-dialog
  [header]="utilizationEmployeeName"
  [(visible)]="utilizationDetails"
  [modal]="true"
  class="fix-dialog"
  [baseZIndex]="10000"
  [style]="{ width: '50vw' }"
  [draggable]="false"
  [dismissableMask]="true"
  [resizable]="false"
>
  <ng-template pTemplate="header">
    {{ utilizationEmployeeName }}
  </ng-template>
  <!-- <div *ngTemplateOutlet="overlayUtilizationDetails"></div> -->
  <div class="my-2"></div>
  <p-table responsiveLayout="scroll" #dt [value]="activeEmployee" dataKey="id" [loading]="loadingEmp" styleClass="p-datatable-customers">
    <ng-template pTemplate="header">
      <tr>
        <th id="company" class="header-width">Client</th>
        <th id="contactPerson" class="contact-person-header-width">Project</th>
        <th id="phone" class="header-width">Start Date</th>
        <th id="email" class="header-width">End Date</th>
        <th class="header-width">Allocation</th>
        <th id="email" class="header-width">Project Status</th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-customer>
      <tr>
        <td>
          {{ customer?.customer?.project?.customer?.name }}
        </td>
        <td>
          <a class="ellipses" *hasAnyPermission="permissionModules.MANAGE_PROJECT; disableEvent: true" [routerLink]="[appRoutes.EDIT_PROJECT, customer.project?.id]">
            <span [title]="customer.project.name">
              {{ customer.project.name }}
            </span>
          </a>
        </td>
        <td>
          {{ customer.start_date | date : 'MM/dd/yyyy' }}
        </td>
        <td>
          {{ customer.end_date | date : 'MM/dd/yyyy' }}
        </td>
        <td>
          {{ customer.allocation }}
        </td>
        <td>
          <span [title]="customer?.customer?.project?.status">
            {{ customer?.customer?.project?.status }}
          </span>
        </td>
      </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
      <tr>
        <td colspan="6" class="text-center">No Data found.</td>
      </tr>
    </ng-template>
  </p-table>
  <ng-template pTemplate="footer">
    <button type="button" class="btn-cancel float-right" (click)="closeModal()">Close</button>
  </ng-template>
</p-dialog>

<ng-template #overlayUtilizationDetails>
  <div class="d-block text-right">
    <button class="btn-close-icon" (click)="closePopOver(popOver)">
      <fa-icon icon="times"></fa-icon>
    </button>
  </div>
</ng-template>
