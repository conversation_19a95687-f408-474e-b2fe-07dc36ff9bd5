import { KeyValue } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, HostListener, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { AlertType } from '@shared/models/alert-type.enum';
import { LayoutConfigService } from '@shared/services/layout-config.service';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { TreeNode } from 'primeng/api';
import { BehaviorSubject, throwError, of } from 'rxjs';
import { BillingTypes, Project, ValidMonthlyProjection, FixedBidPlug, MonthlyTotal, ProjectProjections } from '../project.model';
import { ProjectService } from '../project.service';
import { TableHeader, MONTH_NAMES, FINANCIAL_REVIEW_TYPES } from './financial-review.model';
import { catchError, delay, retryWhen, switchMap, take, map } from 'rxjs/operators';
import { formatDateToYYYYMMDD, getLastDateOfPreviousMonth } from '../../../@shared/utils/date-utils';
import filter from '../../../../assets/plugins/formvalidation/src/js/core/filter';

@Component({
  selector: 'app-financial-review',
  templateUrl: './financial-review.component.html',
  styleUrls: ['./financial-review.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FinancialReviewComponent extends SflBaseComponent implements OnInit, OnDestroy {
  financialReview: TreeNode[] = [];
  @Input() projectId?: number;
  @Input() project: Project;
  projections: ProjectProjections;
  @Input() validationStatus: string;
  @Input() maxDate: Date;
  @Input() minDate: Date;
  @Input() yearRange: any;
  startDateError = false;
  projectionData: ProjectProjections;
  startDate: Date;
  endDate: Date;
  endDateError = false;

  tableHeaders: TableHeader[] = [];
  fixedRevenueTreeData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.FIXED_REVENUE }
  };
  tAndMRevenueTreeData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.T_M_REVENUE },
    children: []
  };
  revenueTreeData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.REVENUE },
    children: []
  };
  // projectExpenseTreeData: TreeNode = {
  //   data: { type: FINANCIAL_REVIEW_TYPES.PROJECT_EXPENSE }
  // };
  peopleExpenseTreeData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.PEOPLE_EXPENSE },
    children: []
  };
  expenseTreeData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.EXPENSE },
    children: []
  };
  grossProfitData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.GROSS_PROFIT }
  };
  grossMarginData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.GROSS_MARGIN }
  };

  frozenCols = [];
  height = 'calc((var(--fixed-content-height, 1vh) * 100) - 100px)';
  resizeFlag = false;
  billingTypes = BillingTypes;
  fixedBidPlugs: FixedBidPlug[] = [];
  retainerPlugs = [];
  showLockDialog = false;
  lockValueObj = null;
  showAmtError = false;
  showNoteError = false;
  loading$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  lockArray = [];
  isRemoveSubmitting = false;
  WorkExpenseTreeData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.WORK_EXCEPTION },
    children: []
  };
  @Output() checkValidationStatus = new EventEmitter();

  constructor(
    private readonly layoutUtilsService: LayoutUtilsService,
    private readonly layoutConfigService: LayoutConfigService,
    private readonly projectService: ProjectService,
    private readonly cdf: ChangeDetectorRef
  ) {
    super();
  }

  // todo further use case
  // ngOnChanges(changes: SimpleChanges): void {
  // if (this.projections && this.validationStatus === 'YES') {
  //   this.init();
  // } else {
  //   this.loading$.next(false);
  //   this.financialReview = [];
  // }
  // }

  ngOnInit(): void {
    this.setYearRange();
    this.resetSelectedMonthFilter();
    // todo further use case
    // if (this.validationStatus === 'YES') {
    // }
    this.getAppliedFilter();
    this.getProjections();
  }

  async init() {
    if (window.innerWidth <= 1024) {
      this.resizeFlag = true;
    } else {
      this.resizeFlag = false;
    }
    this.frozenCols = [{ field: 'type', monthLabel: 'Type' }];
    this.resetCacheValue();
    this.resetTreeData();

    if (this.project?.project?.billing_type === this.billingTypes.FIXED_BID) {
      await this.getFixedBidPlugs();
    } else if (this.project?.project?.billing_type === this.billingTypes.MONTHLY_RETAINER) {
      await this.getRetainerPlugs();
    }
    await this.sortByPositionName();
    this.makeRowsSameHeight();
    this.prepareTableHeaders();
  }

  private resetTreeData(): void {
    this.fixedRevenueTreeData = {
      data: { type: FINANCIAL_REVIEW_TYPES.FIXED_REVENUE }
    };
    this.tAndMRevenueTreeData = {
      data: { type: FINANCIAL_REVIEW_TYPES.T_M_REVENUE },
      children: []
    };
    this.revenueTreeData = {
      data: { type: FINANCIAL_REVIEW_TYPES.REVENUE },
      children: []
    };
    this.expenseTreeData = {
      data: { type: FINANCIAL_REVIEW_TYPES.EXPENSE },
      children: []
    };
    this.peopleExpenseTreeData = {
      data: { type: FINANCIAL_REVIEW_TYPES.PEOPLE_EXPENSE },
      children: []
    };
    this.grossProfitData = {
      data: { type: FINANCIAL_REVIEW_TYPES.GROSS_PROFIT }
    };
    this.grossMarginData = {
      data: { type: FINANCIAL_REVIEW_TYPES.GROSS_MARGIN }
    };
    this.WorkExpenseTreeData = {
      data: { type: FINANCIAL_REVIEW_TYPES.WORK_EXCEPTION },
      children: []
    };
  }

  @HostListener('window:resize', ['$event'])
  onResize(event) {
    if (event.target.innerWidth <= 1024) {
      this.resizeFlag = true;
    } else {
      this.resizeFlag = false;
    }
  }

  async sortByPositionName(): Promise<void> {
    return new Promise((resolve) => {
      this.projections?.projects[0]?.project?.projection?.valid_monthly_projections?.forEach((projection) => {
        const valid_positions = projection?.valid_monthly_projection?.validated_monthly_positions;
        const positionWithoutProjectExpense = valid_positions.filter((position) => position.validated_monthly_position?.position?.name !== 'project_expenses');
        const positionWithProjectExpense = valid_positions.filter((position) => position.validated_monthly_position?.position?.name === 'project_expenses');
        positionWithoutProjectExpense.sort((a, b) => {
          const fa = a?.validated_monthly_position?.position?.name?.toLowerCase();
          const fb = b?.validated_monthly_position?.position?.name?.toLowerCase();

          if (fa < fb) {
            return -1;
          }
          if (fa > fb) {
            return 1;
          }
          return 0;
        });
        positionWithoutProjectExpense.push(positionWithProjectExpense[0]);
        projection.valid_monthly_projection.validated_monthly_positions = positionWithoutProjectExpense;
      });
      resolve();
    });
  }

  makeRowsSameHeight() {
    setTimeout(() => {
      // Only process the currently visible table based on resizeFlag
      const tableId = this.resizeFlag ? '#mobile-table' : '#desktop-table';
      const wrapperSelector = `${tableId} .p-treetable-scrollable-wrapper`;

      const wrapper = document.querySelector(wrapperSelector) as HTMLElement;
      if (wrapper) {
        const frozen_rows: any = wrapper.querySelectorAll('.p-treetable-frozen-view tr');
        const unfrozen_rows: any = wrapper.querySelectorAll('.p-treetable-unfrozen-view tr');
        for (let i = 0; i < frozen_rows.length; i++) {
          if (frozen_rows[i].clientHeight > unfrozen_rows[i].clientHeight) {
            unfrozen_rows[i].style.height = frozen_rows[i].clientHeight + 'px';
          } else if (frozen_rows[i].clientHeight < unfrozen_rows[i].clientHeight) {
            frozen_rows[i].style.height = unfrozen_rows[i].clientHeight + 'px';
          }
        }
        this.layoutConfigService.updateHeight$.next(true);
        this.height = 'calc((var(--fixed-content-height, 1vh) * 100) - 160px)';
      }
    });
  }

  async getRetainerPlugs(): Promise<void> {
    return new Promise<void>((resolve) => {
      let params = { project_id: this.projectId };
      this.subscriptionManager.add(
        this.projectService.getRetainerPlugs(params).subscribe((res) => {
          if (res.data?.retainer_plugs) {
            this.retainerPlugs = res.data.retainer_plugs;
            this.cdf.detectChanges();
          }
          resolve();
        })
      );
    });
  }

  async getFixedBidPlugs(): Promise<void> {
    return new Promise<void>((resolve) => {
      let params = { project_id: this.projectId };
      this.subscriptionManager.add(
        this.projectService.getFixedBidPlugs(params).subscribe((res) => {
          if (res.data?.fixed_bid_plugs) {
            this.fixedBidPlugs = res.data.fixed_bid_plugs;
            this.cdf.detectChanges();
          }
          resolve();
        })
      );
    });
  }

  prepareWorkExceptionsTreeRowData(tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection, monthlyTotalData: MonthlyTotal) {
    this.prepareChildNodeData(tableHeader, validMonthlyProjection, this.WorkExpenseTreeData, 'work_exception_cost');
    this.WorkExpenseTreeData.data[tableHeader.id] = Number(monthlyTotalData.monthly_total.work_exception_cost);
  }

  getWorkExceptionTree() {
    this.WorkExpenseTreeData = { ...this.WorkExpenseTreeData, children: this.getWorkException() };
    return this.WorkExpenseTreeData;
  }

  getWorkException() {
    this.WorkExpenseTreeData.children.forEach((child) => {
      delete child.data.id;
    });
    return this.WorkExpenseTreeData.children;
  }

  prepareTableHeaders() {
    this.resetTreeData();
    const validMonthlyProjections = this.projections?.projects[0]?.project?.projection?.valid_monthly_projections;

    if (validMonthlyProjections) {
      for (let i = 0; i < validMonthlyProjections.length; i++) {
        if (validMonthlyProjections[i]?.valid_monthly_projection?.validated_monthly_positions) {
          validMonthlyProjections[i].valid_monthly_projection.validated_monthly_positions = validMonthlyProjections[
            i
          ]?.valid_monthly_projection?.validated_monthly_positions?.filter((data) => {
            return data !== undefined;
          });
        }

        const monthlyProjection = validMonthlyProjections[i]?.valid_monthly_projection;
        if (monthlyProjection && this.isMonthInSelectedRange(monthlyProjection.month, monthlyProjection.year)) {
          const month = monthlyProjection.month.toString().length === 2 ? monthlyProjection.month : '0' + monthlyProjection.month;
          const tableHeader: TableHeader = {
            month: monthlyProjection.month,
            monthLabel: `${MONTH_NAMES[monthlyProjection.month - 1]} ${monthlyProjection.year}`,
            year: monthlyProjection.year,
            id: Number(`${monthlyProjection.year}${month}`)
          };
          this.tableHeaders = [...this.tableHeaders, tableHeader];
          this.prepareTreeRowData(tableHeader, monthlyProjection, this.projections.totals_monthly[i]);
        }
      }
    }
    const seen = new Set();
    this.tableHeaders = this.tableHeaders.filter((el) => {
      const duplicate = seen.has(el.id);
      seen.add(el.id);
      return !duplicate;
    });
    this.prepareFinancialReview();
    this.loading$.next(false);
    this.cdf.detectChanges();
  }

  private prepareTreeRowData(tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection, monthlyTotalData: MonthlyTotal) {
    this.prepareRevenueTreeRowData(tableHeader, validMonthlyProjection, monthlyTotalData);
    this.prepareExpenseTreeRowData(tableHeader, validMonthlyProjection, monthlyTotalData);
    this.prepareGrossProfitRowData(tableHeader, validMonthlyProjection, monthlyTotalData);
    this.prepareGrossMarginRowData(tableHeader, validMonthlyProjection, monthlyTotalData);
    this.prepareWorkExceptionsTreeRowData(tableHeader, validMonthlyProjection, monthlyTotalData);
  }

  private prepareGrossProfitRowData(tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection, monthlyTotalData: MonthlyTotal) {
    this.grossProfitData.data[tableHeader.id] = Number(monthlyTotalData.monthly_total.net_profit);
  }

  private prepareGrossMarginRowData(tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection, monthlyTotalData: MonthlyTotal) {
    this.grossMarginData.data[tableHeader.id] = Number(monthlyTotalData.monthly_total.percent_gross_margin) * 100;
  }

  private prepareRevenueTreeRowData(tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection, monthlyTotalData: MonthlyTotal) {
    this.revenueTreeData.data[tableHeader.id] = Number(monthlyTotalData.monthly_total.revenue);
    this.prepareTandMRevenueTreeRowData(tableHeader, validMonthlyProjection);
    this.prepareFixedRevenueTreeRowData(tableHeader, validMonthlyProjection);
  }

  private prepareFixedRevenueTreeRowData(tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareNodeData(tableHeader, validMonthlyProjection, this.fixedRevenueTreeData, 'revenue');
  }

  private prepareTandMRevenueTreeRowData(tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareChildNodeData(tableHeader, validMonthlyProjection, this.tAndMRevenueTreeData, 'revenue');
  }

  private prepareExpenseTreeRowData(tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection, monthlyTotalData: MonthlyTotal) {
    this.expenseTreeData.data[tableHeader.id] = Number(monthlyTotalData.monthly_total.cost);
    this.preparePeopleExpenseTreeRowData(tableHeader, validMonthlyProjection);
  }

  // private prepareProjectExpenseTreeRowData(tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
  //   this.prepareNodeData(tableHeader, validMonthlyProjection, this.projectExpenseTreeData, 'expenses');
  // }

  private preparePeopleExpenseTreeRowData(tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareChildNodeData(tableHeader, validMonthlyProjection, this.peopleExpenseTreeData, 'cost');
  }

  private prepareNodeData(tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection, parentTreeNode: TreeNode, valueFieldName: string) {
    parentTreeNode.data[tableHeader.id] = Number(validMonthlyProjection[valueFieldName]);
  }

  private prepareChildNodeData(tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection, parentTreeNode: TreeNode, valueFieldName: string) {
    let projectExpenseRevenue = false;
    parentTreeNode.data[tableHeader.id] = Number(validMonthlyProjection[valueFieldName]);
    for (const [index, validMonthlyPosition] of validMonthlyProjection?.validated_monthly_positions?.entries()) {
      const validatedMonthlyPosition = validMonthlyPosition?.validated_monthly_position;
      let peopleExpensePosition = {};
      const existingRevenuePositionIndex = parentTreeNode?.children?.findIndex((child) => child?.data?.id === validatedMonthlyPosition?.position?.id);

      if (existingRevenuePositionIndex > -1) {
        peopleExpensePosition = parentTreeNode?.children[existingRevenuePositionIndex];
        peopleExpensePosition['data'][tableHeader.id] = Number(validatedMonthlyPosition[valueFieldName]);
        parentTreeNode.children[existingRevenuePositionIndex] = peopleExpensePosition;
      } else {
        if (valueFieldName === 'revenue' && validatedMonthlyPosition.position?.name === 'project_expenses') {
          projectExpenseRevenue = true;
        }
        if (!projectExpenseRevenue && valueFieldName !== 'work_exception_cost') {
          peopleExpensePosition = {
            data: {
              id: validatedMonthlyPosition.position?.id,
              type: validatedMonthlyPosition.position?.name,
              name: `${validatedMonthlyPosition.position?.employee?.first_name || ''} ${validatedMonthlyPosition.position?.employee?.last_name || ''}`
            }
          };
          peopleExpensePosition['data'][tableHeader.id] = Number(validatedMonthlyPosition[valueFieldName]);
          parentTreeNode.children = [...parentTreeNode.children, peopleExpensePosition];
        }
        if (valueFieldName === 'work_exception_cost' && validatedMonthlyPosition.position?.name !== 'project_expenses') {
          peopleExpensePosition = {
            data: {
              id: validatedMonthlyPosition.position?.id,
              name: `${validatedMonthlyPosition.position?.employee?.first_name || ''} ${validatedMonthlyPosition.position?.employee?.last_name || ''}`
            }
          };
          peopleExpensePosition['data'][tableHeader.id] = Number(validatedMonthlyPosition[valueFieldName]);
          parentTreeNode.children = [...parentTreeNode.children, peopleExpensePosition];
        }
      }
    }
  }

  private addZeroToNonAvailableData() {
    for (const treeNodeRowData of this.financialReview) {
      this.addNotFoundHeaderKeyValue(treeNodeRowData.data);
      const rowDataChildren = treeNodeRowData.children;
      this.addZeroToTreeChild(rowDataChildren);
    }
  }

  private addZeroToTreeChild(children) {
    while (children && children.length) {
      for (const rowDataChild of children) {
        this.addNotFoundHeaderKeyValue(rowDataChild?.data);
        children = rowDataChild.children;
        if (children) {
          this.addZeroToTreeChild(children);
        }
      }
    }
  }

  private addNotFoundHeaderKeyValue(data) {
    if (data) {
      let rowDataKeys: any[] = Object.keys(data);
      rowDataKeys = rowDataKeys.map((key) => Number(key));
      const notFoundHeaderKeys = this.tableHeaders.filter((tableHeader) => !rowDataKeys.includes(tableHeader.id)).map((tableHeader) => tableHeader.id);
      if (notFoundHeaderKeys && notFoundHeaderKeys.length) {
        for (const notFoundHeaderKey of notFoundHeaderKeys) {
          data[notFoundHeaderKey] = 0;
        }
      }
    }
  }

  prepareFinancialReview() {
    this.setLockArray();

    // Define the desired order for COGS section
    const orderPriority = {
      'Work Exceptions': 1,
      'project_expenses': 2
      // All other items will be sorted alphabetically after these priority items
    };

    // Separate children by type
    const expenseTreeWithOutProjectExpense = this.expenseTree.children.filter((child) => child.data.type !== 'project_expenses');
    const expenseTreeWithProjectExpense = this.expenseTree.children.filter((child) => child.data.type === 'project_expenses');

    // Sort all children with custom order that preserves project_expenses position
    const allChildren = [...expenseTreeWithOutProjectExpense, ...expenseTreeWithProjectExpense];

    allChildren.sort((a, b) => {
      const typeA = a.data.type || a.data.name || '';
      const typeB = b.data.type || b.data.name || '';

      const priorityA = orderPriority[typeA] || 999;
      const priorityB = orderPriority[typeB] || 999;

      // If both have priorities, sort by priority
      if (priorityA !== 999 && priorityB !== 999) {
        return priorityA - priorityB;
      }

      // If one has priority and other doesn't, priority item comes first
      if (priorityA !== 999) return -1;
      if (priorityB !== 999) return 1;

      // If neither has priority, sort alphabetically
      const nameA = (a.data.name || a.data.type || '').toLowerCase();
      const nameB = (b.data.name || b.data.type || '').toLowerCase();
      return nameA.localeCompare(nameB);
    });

    this.expenseTree.children = allChildren;
    this.financialReview = [this.revenueTree, this.expenseTree, this.grossProfitData, this.grossMarginData];

    this.layoutConfigService.updateHeight$.next(true);
    this.height = 'calc((var(--fixed-content-height, 1vh) * 100) - 160px)';
    this.addZeroToNonAvailableData();
  }

  get revenueTree(): TreeNode {
    this.tAndMRevenueTreeData.children.forEach((child) => {
      delete child.data.id;
    });
    this.revenueTreeData = {
      ...this.revenueTreeData,
      expanded: false,
      children: this.tAndMRevenueTreeData.children
    };
    return this.revenueTreeData;
  }

  get expenseTree(): TreeNode {
    // Check if work exception tree is already added to prevent duplication
    const workExceptionExists = this.peopleExpenseTreeData.children.some((child) => child.data.type === FINANCIAL_REVIEW_TYPES.WORK_EXCEPTION);

    if (!workExceptionExists) {
      this.peopleExpenseTreeData.children.push(this.getWorkExceptionTree());
    }

    this.peopleExpenseTreeData.children.forEach((child) => {
      delete child.data.id;
    });
    this.expenseTreeData = {
      ...this.expenseTreeData,
      expanded: false,
      children: [...this.peopleExpenseTreeData.children]
    };
    return this.expenseTreeData;
  }

  get isProjectTandM(): boolean {
    return this.project?.project?.billing_type === BillingTypes.TIME_MATERIALS;
  }

  getStyle(rowNode) {
    if (rowNode?.level === 0) {
      return 'font-weight-bold';
    }
    if (rowNode?.level === 1) {
      return 'children-level-1';
    }
  }

  getMonthlyValues(rowData: TreeNode) {
    const montlyData = JSON.parse(JSON.stringify(rowData));
    const typeExcluded = this.omit('type', montlyData);
    const nameExcluded = this.omit('name', typeExcluded);
    const filteredData = {};
    Object.keys(nameExcluded).forEach((key) => {
      const year = parseInt(key.substring(0, 4));
      const month = parseInt(key.substring(4, 6));
      if (this.isMonthInSelectedRange(month, year)) {
        filteredData[key] = nameExcluded[key];
      }
    });

    return filteredData;
  }

  private omit(key, obj) {
    const { [key]: omitted, ...rest } = obj;
    return rest;
  }

  preserveOriginalOrder = (a: KeyValue<string, string>, b: KeyValue<string, string>): number => {
    return 0;
  };

  getTotal(rowData: TreeNode) {
    if (rowData.type === FINANCIAL_REVIEW_TYPES.GROSS_MARGIN) {
      return +this.projections.total['percent_gross_margin'] * 100;
    } else if (rowData?.type === FINANCIAL_REVIEW_TYPES.REVENUE) {
      return this.projections.total['revenue'];
    } else if (rowData?.type === FINANCIAL_REVIEW_TYPES.EXPENSE) {
      return this.projections.total['cost'];
    } else if (rowData?.type === FINANCIAL_REVIEW_TYPES.GROSS_PROFIT) {
      return this.projections.total['gross_margin'];
    } else if (rowData?.type === FINANCIAL_REVIEW_TYPES.WORK_EXCEPTION) {
      return this.projections.total['work_exception_cost'];
    } else {
      // Use the filtered monthly values for calculating totals
      const filteredMonthlyValues = this.getMonthlyValues(rowData);
      if (Object.keys(filteredMonthlyValues).length) {
        return this.sumValues(filteredMonthlyValues);
      } else {
        return 0;
      }
    }
  }

  sumValues = (obj) => Object.values(obj)?.reduce((a?: any, b?: any) => a + b);

  openRevenueLockDialog(rowNode, rowData, monthlyData, index) {
    this.lockValueObj = null;
    this.showLockDialog = true;
    let month = Number(monthlyData.key.slice(-2));
    let year = Number(monthlyData.key.slice(0, 4));
    this.loading$$.next(true);
    let existsLock = this.lockArray.filter((arr) => arr.key === Number(index.key));
    if (!existsLock.length) {
      this.lockValueObj = {
        title: 'Lock',
        displayText: 'Locking the revenue on a fixed bid project ensures that the recognized revenue will not change regardless of project changes',
        displayMonth: MONTH_NAMES[monthlyData.key.slice(-2) - 1] + ' ' + monthlyData.key.slice(0, 4),
        amount: Math.round(monthlyData.value),
        month: month,
        year: year,
        project_id: Number(this.projectId),
        reason: null
      };
      this.loading$$.next(false);
    } else {
      if (existsLock[0].value === 'unlock') {
        this.lockValueObj = {
          title: 'Lock',
          displayText: 'Locking the revenue on a fixed bid project ensures that the recognized revenue will not change regardless of project changes',
          displayMonth: MONTH_NAMES[monthlyData.key.slice(-2) - 1] + ' ' + monthlyData.key.slice(0, 4),
          amount: Math.round(monthlyData.value),
          month: month,
          year: year,
          project_id: Number(this.projectId),
          reason: null
        };
        this.loading$$.next(false);
      } else {
        let params = {
          project_id: this.projectId,
          month: month === 12 ? 1 : month + 1,
          year: month === 12 ? year + 1 : year
        };
        this.subscriptionManager.add(
          this.projectService.getFixedBidPlugs(params).subscribe((res) => {
            // Next Month Revenue is not Locked
            if (!res?.data?.fixed_bid_plugs) {
              let params = {
                project_id: this.projectId,
                month: month,
                year: year
              };
              this.subscriptionManager.add(
                this.projectService.getFixedBidPlugs(params).subscribe((res) => {
                  this.lockValueObj = {
                    id: res.data.fixed_bid_plugs[0].fixed_bid_plug.id,
                    title: 'Lock Edit',
                    displayText: 'Locking the revenue on a fixed bid project ensures that the recognized revenue will not change regardless of project changes',
                    displayMonth: MONTH_NAMES[monthlyData.key.slice(-2) - 1] + ' ' + monthlyData.key.slice(0, 4),
                    amount: Math.round(Number(res.data.fixed_bid_plugs[0].fixed_bid_plug.amount)),
                    month: month,
                    year: year,
                    project_id: Number(this.projectId),
                    reason: res.data.fixed_bid_plugs[0].fixed_bid_plug.reason,
                    isEdit: true
                  };
                  this.loading$$.next(false);
                  this.cdf.detectChanges();
                })
              );
            }
            // Next Month Revenue is Locked
            else {
              let m = month === 12 ? 1 : month + 1;
              let y = month === 12 ? year + 1 : year;
              this.lockValueObj = {
                title: 'Lock Edit',
                displayText: `You can only update most recent lock.`,
                locked: true // do not allow update remove both the input field and show only okay button
              };
              this.loading$$.next(false);
              this.cdf.detectChanges();
            }
          })
        );
      }
    }
    if (this.retainerPlugs.length > 0 && this.project?.project?.billing_type === this.billingTypes.MONTHLY_RETAINER) {
      let retainerPlugs = this.retainerPlugs.find((res) => res.retainer_plug.month === month && res.retainer_plug.year === year);
      this.lockValueObj = {
        title: 'Adjust Revenue',
        displayText: 'Adjusting the revenue on a retainer project ensures that the recognized revenue for this month will not change regardless of project changes.',
        displayMonth: MONTH_NAMES[monthlyData.key.slice(-2) - 1] + ' ' + monthlyData.key.slice(0, 4),
        amount: Math.round(monthlyData.value),
        month: month,
        year: year,
        project_id: Number(this.projectId),
        reason: retainerPlugs?.retainer_plug?.reason,
        id: retainerPlugs?.retainer_plug?.id
      };
    }
  }

  setLockRetainerData() {
    for (let i = 0; i < this.tableHeaders.length; i++) {
      if (!this.retainerPlugs.length) {
        this.lockArray = [{ key: this.tableHeaders[i].id, value: 'unlock' }];
        break;
      }
    }
  }

  setLockArray() {
    for (let i = 0; i < this.tableHeaders.length; i++) {
      if (this.fixedBidPlugs) {
        if (!this.fixedBidPlugs.length && i === 0) {
          this.lockArray = [{ key: this.tableHeaders[i].id, value: 'unlock' }];
          break;
        } else {
          if (i === this.tableHeaders.length - 1) {
            break;
          } else if (this.fixedBidPlugs.length === i) {
            this.lockArray = [...this.lockArray, { key: this.tableHeaders[i].id, value: 'unlock' }];
            break;
          } else {
            this.lockArray = [...this.lockArray, { key: this.tableHeaders[i].id, value: 'lock' }];
          }
        }
      }
      if (this.retainerPlugs.length !== 0) {
        if (i === this.tableHeaders.length - 1) {
          break;
        } else if (this.fixedBidPlugs.length === i) {
          this.lockArray = [...this.lockArray, { key: this.tableHeaders[i].id, value: 'unlock' }];
          break;
        } else {
          this.lockArray = [...this.lockArray, { key: this.tableHeaders[i].id, value: 'lock' }];
        }
      }
    }
  }

  getLockUnlockSign(monthlyData, index) {
    for (let i = 0; i < this.lockArray.length; i++) {
      if (this.lockArray[i].key === Number(index.key)) {
        if (this.lockArray[i].value === 'unlock') {
          return 'unlock';
        } else if (this.lockArray[i].value === 'lock') {
          return 'lock';
        } else {
          return null;
        }
      }
    }
  }

  removeLock() {
    this.isRemoveSubmitting = true;
    this.subscriptionManager.add(
      this.projectService.deleteFixedBidPlug(this.lockValueObj.id).subscribe(
        (res) => {
          this.layoutUtilsService.showActionNotification('Fixed Bid Lock Removed successfully', AlertType.Success);
          this.showLockDialog = false;
          this.isRemoveSubmitting = false;
          this.closeLockModal();
          this.getProjections();
          this.cdf.detectChanges();
        },
        () => (this.isRemoveSubmitting = false)
      )
    );
  }

  saveLockValue() {
    let flag = true;
    if (this.lockValueObj.amount === null || this.lockValueObj.amount === undefined) {
      flag = false;
      this.showAmtError = true;
    }
    if (!this.lockValueObj.reason) {
      flag = false;
      this.showNoteError = true;
    }
    if (flag) {
      this.isSubmitting = true;
      const lockObj = { ...this.lockValueObj };
      delete lockObj['title'];
      delete lockObj['displayMonth'];
      delete lockObj['displayText'];
      delete lockObj['isEdit'];
      let service = '';
      if (this.lockValueObj.isEdit && this.project?.project?.billing_type === this.billingTypes.FIXED_BID) {
        service = 'updateFixedBidPlug';
      } else if (!this.lockValueObj.isEdit && this.project?.project?.billing_type === this.billingTypes.FIXED_BID) {
        service = 'addFixedBidPlug';
      } else if (this.project?.project?.billing_type === this.billingTypes.MONTHLY_RETAINER) {
        service = 'updateRetainerPlugs';
      }
      this.subscriptionManager.add(
        this.projectService[service](lockObj).subscribe(
          async (res) => {
            this.tableHeaders = [];
            if (service === 'updateFixedBidPlug' || service === 'addFixedBidPlug') {
              this.layoutUtilsService.showActionNotification(`Fixed Bid Lock ${this.lockValueObj.isEdit ? 'updated' : 'created'} successfully`, AlertType.Success);
            } else {
              this.layoutUtilsService.showActionNotification(`retainer plug updated successfully`, AlertType.Success);
            }
            this.checkValidationStatus.emit();
            this.showLockDialog = false;
            this.isSubmitting = false;
            this.closeLockModal();
            this.getProjections();
            this.cdf.detectChanges();
          },
          (err) => {
            this.isSubmitting = false;
            this.layoutUtilsService.showActionNotification(err?.error?.message, AlertType.Error, 5000);
            this.cdf.detectChanges();
          }
        )
      );
    }
  }

  closeLockModal() {
    this.showLockDialog = false;
    this.loading$$.next(false);
    this.lockValueObj = null;
    this.isSubmitting = false;
    this.isRemoveSubmitting = false;
  }

  valueChange(key) {
    if (key === 'amount') {
      this.showAmtError = false;
    }
    if (key === 'reason') {
      this.showNoteError = false;
    }
  }

  clickableIcon(monthlyData, index) {
    if (this.getLockUnlockSign(monthlyData, index) === 'lock' || this.getLockUnlockSign(monthlyData, index) === 'unlock') {
      return true;
    } else {
      return false;
    }
  }

  resetSelectedMonthFilter(): void {
    this.startDate = this.minDate;
    this.endDate = this.maxDate;
    this.onDateChange();
  }

  setYearRange(): void {
    this.yearRange = this.minDate.getFullYear() + ':' + this.maxDate.getFullYear();
  }

  getProjections(): void {
    let flag = 0;
    const params = {
      projection_detail_level: 'position_monthly',
      include_work_exceptions: true,
      include_utilizations: false,
      start_date: formatDateToYYYYMMDD(this.startDate),
      end_date: formatDateToYYYYMMDD(getLastDateOfPreviousMonth(this.endDate))
    };
    this.resetTreeData();
    this.resetCacheValue();
    if (this.projections?.projects[0]?.project?.projection?.valid_monthly_projections) {
      this.projections.projects[0].project.projection.valid_monthly_projections = [];
    }
    this.subscriptionManager.add(
      this.projectService
        .getProjections(this.projectId, params)
        .pipe(
          map((res) => {
            if (res.status === 202) {
              flag++;
              throw res;
            }
            return res;
          }),
          retryWhen((errors) =>
            errors.pipe(
              switchMap((val) => {
                if (flag >= 15 && (val.status === 202 || val?.error?.status === 202)) {
                  return of(val).pipe(delay(15000), take(2));
                } else if (flag < 15 && (val.status === 202 || val?.error?.status === 202)) {
                  return of(val).pipe(delay(1000), take(15));
                } else {
                  return throwError(errors);
                }
              })
            )
          ),
          catchError((err) => throwError(err))
        )
        .pipe(
          map((res) => {
            if (res.body.message !== this.appConstants.apiMessage.requestINProgress) {
              if (this.startDate && this.endDate) {
                const startYear = this.startDate.getFullYear();
                const startMonth = this.startDate.getMonth() + 1;
                const endYear = this.endDate.getFullYear();
                const endMonth = this.endDate.getMonth() + 1;
                res.body.data.projects[0].project.projection.valid_monthly_projections = res.body.data.projects[0].project.projection.valid_monthly_projections.filter(
                  (projection) => {
                    const projYear = projection.valid_monthly_projection.year;
                    const projMonth = projection.valid_monthly_projection.month;
                    return (projYear > startYear || (projYear === startYear && projMonth >= startMonth)) && (projYear < endYear || (projYear === endYear && projMonth <= endMonth));
                  }
                );
                res.body.data.totals_monthly = res.body.data.totals_monthly.filter((monthly_total) => {
                  const totalYear = monthly_total.monthly_total.year;
                  const totalMonth = monthly_total.monthly_total.month;
                  return (
                    (totalYear > startYear || (totalYear === startYear && totalMonth >= startMonth)) && (totalYear < endYear || (totalYear === endYear && totalMonth <= endMonth))
                  );
                });
              }
              return res;
            }
          })
        )
        .subscribe((res) => {
          if (res.body.message !== this.appConstants.apiMessage.requestINProgress) {
            this.financialReview = [];
            this.tableHeaders = [];
            this.projections = undefined;
            this.projectionData = undefined;

            this.projectionData = res.body.data;
            this.projections = this.projectionData;
            this.init();
            this.cdf.detectChanges();
          }
        })
    );
  }

  onDateChange(): void {
    if (this.onMonthValidation()) {
      this.resetTreeData();
      this.getProjections();
    }
  }

  onMonthValidation(): boolean {
    if (this.startDate <= this.endDate) {
      this.startDateError = false;
      this.endDateError = false;
      return true;
    } else {
      this.startDateError = true;
      this.endDateError = true;
      return false;
    }
  }

  private isMonthInSelectedRange(month: number, year: number): boolean {
    if (!this.startDate || !this.endDate) {
      return true;
    }

    const startYear = this.startDate.getFullYear();
    const startMonth = this.startDate.getMonth() + 1;
    const endYear = this.endDate.getFullYear();
    const endMonth = this.endDate.getMonth() + 1;
    return (year > startYear || (year === startYear && month >= startMonth)) && (year < endYear || (year === endYear && month <= endMonth));
  }

  saveAppliedFilter(): void {
    localStorage.setItem(
      this.appConstants.storageConst.financialReviewFilter,
      JSON.stringify({ startDate: this.startDate, endDate: this.endDate, minDate: this.minDate, maxDate: this.maxDate, projectId: this.projectId })
    );
  }
  getAppliedFilter(): void {
    const appliedFilter = localStorage.getItem(this.appConstants.storageConst.financialReviewFilter) ? JSON.parse(localStorage.getItem('financialReviewFilter')) : null;
    if (appliedFilter && this.projectId === appliedFilter?.projectId) {
      if (this.isMonthInRange(new Date(appliedFilter.startDate), this.minDate, this.maxDate) && this.isMonthInRange(new Date(appliedFilter.endDate), this.minDate, this.maxDate)) {
        this.startDate = new Date(appliedFilter.startDate);
        this.endDate = new Date(appliedFilter.endDate);
      }
    }
  }

  ngOnDestroy(): void {
    this.saveAppliedFilter();
  }

  isMonthInRange(inputDate: Date, startDateStr: Date, endDateStr: Date): boolean {
    return inputDate >= startDateStr && inputDate <= endDateStr;
  }

  resetCacheValue(): void {
    this.financialReview = [];
    this.tableHeaders = [];
    this.fixedBidPlugs = [];
    this.retainerPlugs = [];
    this.lockArray = [];
  }
}
