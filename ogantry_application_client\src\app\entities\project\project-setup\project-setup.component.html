<form class="form mt-0" id="kt_form">
  <!--begin: Form Wizard Step 1-->
  <div data-wizard-type="step-content" data-wizard-state="current" id="project_setup_form" style="position: relative">
    <div class="card-body scrollable-content" *isFetchingData="loading$">
      <ngb-accordion #acc="ngbAccordion" activeIds="custom-panel-1" (panelChange)="panelChange($event)">
        <ngb-panel id="custom-panel-1">
          <ng-template ngbPanelHeader let-opened="opened">
            <div class="d-flex align-items-center justify-content-between border-bottom">
              <h5 class="pl-4">Create Project</h5>
              <button ngbPanelToggle class="btn btn-link">
                <fa-icon icon="chevron-down"></fa-icon>
              </button>
            </div>
          </ng-template>
          <ng-template ngbPanelContent>
            <form
              class="form border-bottom"
              [formGroup]="projectSetupForm"
              autocomplete="off"
              novalidate="novalidate"
              id="create_project_setup_form"
              (ngSubmit)="createProjectSetup()"
            >
              <div id="project-setup">
                <kt-auth-notice></kt-auth-notice>
                <div class="row">
                  <!-- Project Details begins -->
                  <div class="col-lg-6 col-12 pr-md-5">
                    <div class="form-group first client-dropdown">
                      <label class="form-label">Client</label>
                      <div class="form-group">
                        <div class="form-check form-check-inline">
                          <input class="form-check-input" type="radio" formControlName="clientFlag" id="exist" value="exist" (click)="clientValue($event)" />
                          <label class="form-check-label" for="exist"> Existing Client </label>
                        </div>
                        <div class="form-check form-check-inline">
                          <input class="form-check-input" type="radio" formControlName="clientFlag" id="new" value="new" (click)="clientValue($event)" />
                          <label class="form-check-label" for="new"> New Client </label>
                        </div>
                      </div>
                      <input
                        id="typeahead-prevent-manual-entry"
                        placeholder="Client Name"
                        type="text"
                        class="form-control typehead"
                        [ngbTypeahead]="search"
                        [inputFormatter]="formatter"
                        [resultFormatter]="formatter"
                        formControlName="customer_id"
                        (focus)="focus$.next($any($event).target.value)"
                        (click)="click$.next($any($event).target.value)"
                        #instance="ngbTypeahead"
                        [editable]="false"
                      />
                      <app-form-error [validation]="'required'" [form]="projectSetupForm" [controlName]="'customer_id'" [fieldLabel]="'Client Name'"></app-form-error>
                    </div>

                    <div class="form-group">
                      <label class="form-label">Project</label>
                      <input type="text" class="form-control custom" required placeholder="e.g. Mobile Application" formControlName="name" />
                      <app-form-error [validation]="'required'" [form]="projectSetupForm" [controlName]="'name'" [fieldLabel]="'Project Name'"></app-form-error>
                    </div>

                    <div class="form-group">
                      <label class="form-label">Project Description</label>
                      <textarea class="form-control custom" placeholder="Type description here..." rows="5" formControlName="description"></textarea>
                      <app-form-error [validation]="'required'" [form]="projectSetupForm" [controlName]="'description'" [fieldLabel]="'Project Description'"></app-form-error>
                    </div>
                    <div class="form-group first client-dropdown2">
                      <label class="form-label">Project Status</label>
                      <div ngbDropdown class="d-inline-block w-100" display="dynamic">
                        <button
                          type="button"
                          class="btn btn-outline-primary dropdown-btn dropdown-height"
                          id="dropdownBasic2"
                          ngbDropdownToggle
                          [ngClass]="{ 'dropdown-text': projectStatus }"
                        >
                          {{ projectStatus ? projectStatus : 'Status' }}
                          <em class="pi pi-chevron-down" aria-hidden="true"></em>
                        </button>
                        <div *hasAnyPermission="permissionModules.MANAGE_PROJECT_STATUS; disableEvent: true"></div>

                        <div ngbDropdownMenu aria-labelledby="dropdownBasic2">
                          <button type="button" ngbDropdownItem *ngFor="let status of status" (click)="setProjectStatus(status)">
                            {{ status }}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-6 col-12 pr-md-5" id="i am hear 1">
                    <div class="row">
                      <div class="col-12">
                        <div class="form-group pb-4">
                          <label class="form-label">Project Type</label>
                        </div>
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-12">
                        <div class="form-group pt-0">
                          <div class="form-check form-check-inline" *ngFor="let item of bill">
                            <input class="form-check-input" type="radio" [id]="item" [value]="item" formControlName="billing_type" (click)="billRateChange(item, true)" />
                            <label class="form-check-label" [for]="item">
                              {{ item === 'Retainer' ? 'Monthly Retainer' : item }}
                            </label>
                          </div>
                          <app-form-error [validation]="'required'" [form]="projectSetupForm" [controlName]="'billing_type'" [fieldLabel]="'Bill Rate'"></app-form-error>
                        </div>
                      </div>
                    </div>
                    <div class="row" [ngClass]="{ 'd-inline-block': projectSetupForm.value.billing_type === billingTypes.MONTHLY_RETAINER && projectId }">
                      <ng-container *ngIf="projectSetupForm.value.billing_type === billingTypes.FIXED_BID || projectSetupForm.value.billing_type === billingTypes.MONTHLY_RETAINER">
                        <div class="mb-3">
                          <div class="col-12 input-group">
                            <div class="input-group-prepend">
                              <span class="input-group-text">$</span>
                            </div>
                            <p-inputNumber
                              sflIsNumber
                              class="form-control amount-form-control"
                              [placeholder]="projectSetupForm.value.billing_type === billingTypes.FIXED_BID ? 'Fixed Bid Total' : 'Default Monthly Revenue'"
                              formControlName="amount"
                              (onKeyDown)="onAmountChange()"
                            ></p-inputNumber>
                          </div>
                          <div class="pl-4 d-flex">
                            <app-form-error
                              class="pl-3"
                              *ngIf="projectSetupForm.value.billing_type === billingTypes.FIXED_BID || projectSetupForm.value.billing_type === billingTypes.MONTHLY_RETAINER"
                              [validation]="'required'"
                              [form]="projectSetupForm"
                              [controlName]="'amount'"
                              [fieldLabel]="'Amount'"
                            ></app-form-error>
                          </div>
                        </div>
                        <div *ngIf="projectSetupForm.value.billing_type === billingTypes.MONTHLY_RETAINER && projectId" class="d-flex pl-3">
                          <button type="button" class="btn btn-primary ml-2 adjust-revenue-btn" (click)="openRetainerPlugDialog()">Adjust Current Projected Revenue</button>
                        </div>
                      </ng-container>
                    </div>
                    <!-- <a
                      href="javascript:;void"
                      class="add-new"
                      (click)="openExtendFormVisible()"
                      ><strong>+ More Fields </strong></a
                    > -->

                    <div class="row">
                      <div class="col-12">
                        <app-extended-form
                          #extendFrom
                          [projectId]="projectId"
                          [componentType]="componentType.Project"
                          [extendFieldsObj]="extendFieldsObj"
                          (dataChangeEvent)="extendFieldsDataChange()"
                        ></app-extended-form>
                      </div>
                    </div>
                  </div>

                  <!-- Project Details ends -->
                </div>
              </div>
              <div class="d-flex justify-content-end py-5 px-10 btn-pos" *ngIf="!isSave">
                <div>
                  <button type="submit" class="btn btn-success font-weight-bold text-uppercase px-9 py-4 btn-height" [isSubmitting]="isSubmitting">
                    {{ projectId ? 'Save' : 'Create' }} Project
                  </button>
                </div>
              </div>
            </form>
          </ng-template>
        </ngb-panel>
        <ngb-panel id="custom-panel-2" *ngIf="isSave || isEdit">
          <ng-template ngbPanelHeader let-opened="opened">
            <div class="d-flex align-items-center justify-content-between border-bottom">
              <h5 class="pl-4">Contacts</h5>
              <button ngbPanelToggle class="btn btn-link">
                <fa-icon icon="chevron-down"></fa-icon>
              </button>
            </div>
          </ng-template>
          <ng-template ngbPanelContent>
            <!-- Contact person begins -->
            <div class="col-lg-12 col-12 pl-md-5 border-bottom" *ngIf="isSave || isEdit">
              <ng-container *ngTemplateOutlet="contactPersonDetails"></ng-container>
            </div>
            <!-- Contact person ends -->
          </ng-template>
        </ngb-panel>
        <ngb-panel id="custom-panel-3" *ngIf="isSave || isEdit">
          <ng-template ngbPanelHeader let-opened="opened">
            <div class="d-flex align-items-center justify-content-between border-bottom">
              <h5 class="pl-4">Project Expenses</h5>
              <button ngbPanelToggle class="btn btn-link">
                <fa-icon icon="chevron-down"></fa-icon>
              </button>
            </div>
          </ng-template>
          <ng-template ngbPanelContent>
            <div *ngIf="isSave || isEdit" class="border-bottom">
              <ng-container *ngTemplateOutlet="projectExpense"></ng-container>
            </div>
          </ng-template>
        </ngb-panel>
        <ngb-panel id="custom-panel-4" *ngIf="isSave || isEdit">
          <ng-template ngbPanelHeader let-opened="opened">
            <div class="d-flex align-items-center justify-content-between border-bottom mb-3">
              <h5 class="pl-4">Project Tags</h5>
              <button ngbPanelToggle class="btn btn-link">
                <fa-icon icon="chevron-down"></fa-icon>
              </button>
            </div>
          </ng-template>
          <ng-template ngbPanelContent>
            <div *ngIf="isSave || isEdit" class="border-bottom">
              <app-tag-listing-view [_employeeId]="projectId" [_tags]="_tags" [_flag]="'PROJECT_TAG'" (_reloadTags)="getProject()"></app-tag-listing-view>
            </div>
          </ng-template>
        </ngb-panel>
      </ngb-accordion>
    </div>
    <div class="fixed-content" [hidden]="!isSave" [ngClass]="{ 'd-flex justify-content-end py-5 px-10 btn-pos': isSave }">
      <div>
        <div type="button" class="btn btn-primary font-weight-bold text-uppercase px-9 py-4 btn-height" data-wizard-type="action-next" [hidden]="!isSave" (click)="nextStep()">
          Next
        </div>
      </div>
    </div>
  </div>
</form>

<ng-template #contactPersonDetails>
  <div class="form-group">
    <app-contact-person
      [projectContact]="true"
      (showDeleteModal)="showModal($event)"
      [contactPersons]="projectData?.project?.contacts"
      (updateContactSideNav)="openSidebar($event, true)"
    >
    </app-contact-person>
    <a href="javascript:;void" class="add-new" (click)="openSidebar()"><strong>+ Add New</strong></a>
  </div>
</ng-template>

<p-dialog header="Delete Project" [(visible)]="showDeleteDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0">Are you sure you want to delete this Contact Person?</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="deleteContact()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

<ng-template #projectExpense>
  <div class="overlay">
    <div [ngClass]="{ 'overlay-wrapper': !hasPositionFlag }">
      <form class="p-m-0 mt-2 mb-2 form" [formGroup]="addExpenseForm" autocomplete="off" novalidate="novalidate" id="add_project_expense_form">
        <p-table
          #dt1
          [value]="projectExpenseList"
          dataKey="id"
          [loading]="expenseLoader"
          styleClass="p-datatable-customers"
          [filterDelay]="0"
          [style]="{ overflow: 'auto!important' }"
          editMode="row"
        >
          <ng-template pTemplate="header">
            <tr>
              <th id="plus-icon" class="header-width-plus">
                <a href="javascript:;void" class="form-group btn btn-icon btn-light btn-plus btn-sm svg-icon-white" (click)="showAddExpenseRow()">
                  <span title="Add Position" [inlineSVG]="'assets/media/svg/icons/plus.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
                </a>
              </th>
              <th id="type" class="header-width">Type</th>
              <th id="cost" class="header-width">Cost</th>
              <th id="description" class="header-width">Description</th>
              <th id="expenseType" class="header-width">Expense Type</th>
              <th id="month" class="month-section">Month/Year</th>
              <th id="actions" class="action-width"></th>
            </tr>
            <tr class="add-expense-height" *ngIf="showAddExpenseRowFlag">
              <th class="background-white"></th>
              <th id="addType" class="background-white">
                <p-dropdown
                  appendTo="body"
                  [options]="expenses"
                  formControlName="type"
                  styleClass="p-column-filter pi-icon form-control-custom"
                  placeholder="Select Expense"
                  (onChange)="expenseSelected($event)"
                >
                </p-dropdown>
              </th>
              <th id="addCost" class="background-white">
                <ng-container *ngIf="allowExpenseSelection">
                  <input pInputText type="search" sflIsNumber class="form-control-custom" formControlName="cost" placeholder="0.00" />
                  <app-form-error class="position-rel" [validation]="'required'" [form]="addExpenseForm" [controlName]="'cost'" [fieldLabel]="'Amount'"></app-form-error>
                </ng-container>
              </th>
              <th id="addDescription" class="background-white">
                <ng-container *ngIf="allowExpenseSelection">
                  <input pInputText type="search" class="form-control-custom" formControlName="description" placeholder="Description" />
                  <app-form-error class="position-rel" [validation]="'required'" [form]="addExpenseForm" [controlName]="'description'" [fieldLabel]="'Description'">
                  </app-form-error>
                </ng-container>
              </th>
              <th id="addExpenseType" class="background-white">
                <ng-container *ngIf="allowExpenseSelection">
                  <p-dropdown
                    appendTo="body"
                    [options]="showDailyExpenseType ? dailyExpenseTypes : monthlyExpenseTypes"
                    formControlName="type_id"
                    styleClass="p-column-filter pi-icon form-control-custom"
                    placeholder="Expense Type"
                  >
                  </p-dropdown>
                  <app-form-error class="position-rel" [validation]="'required'" [form]="addExpenseForm" [controlName]="'type_id'" [fieldLabel]="'Expense Type'"> </app-form-error>
                </ng-container>
              </th>
              <th id="addMonth" class="calender-month background-white">
                <ng-container *ngIf="allowExpenseSelection && !showDailyExpenseType" class="form-group">
                  <div class="month-calendar-wrapper position-relative">
                    <div class="selected-months-display apply-bg-white">
                      <ng-container *ngIf="selectedMonths?.length > 0">
                        <p-chip
                          *ngFor="let date of selectedMonths"
                          [label]="getFormattedMonthYear(date)"
                          [removable]="true"
                          (onRemove)="removeSelectedMonth(date)"
                          styleClass="month-chip"
                          [attr.style]="'z-index: 3'"
                        ></p-chip>
                      </ng-container>
                    </div>
                    <div id="custom-month-edit" class="custom-month-selection fix-calender-pos">
                      <p-calendar
                        #customMonthCalender
                        id="custom-month-calendar"
                        [(ngModel)]="selectedMonths"
                        [ngModelOptions]="{ standalone: true }"
                        selectionMode="multiple"
                        view="month"
                        dateFormat="M/y"
                        [showIcon]="true"
                        [yearNavigator]="true"
                        [monthNavigator]="true"
                        [readonlyInput]="true"
                        [style]="{ width: '100%' }"
                        appendTo="body"
                        [showClear]="true"
                        showButtonBar="true"
                        placeholder="Month/Year"
                        (onShow)="highlightSelectedMonths(); hideCalendarNavButtons()"
                        (ngModelChange)="highlightSelectedMonths($event); onMonthSelect()"
                        (onViewChange)="highlightSelectedMonths()"
                        (onClearClick)="highlightSelectedMonths()"
                        (onYearChange)="highlightSelectedMonths()"
                      ></p-calendar>
                    </div>
                  </div>
                  <div class="position-absolute pl-4">
                    <small class="text-danger" *ngIf="!selectedMonths?.length">Please select at least one month.</small>
                  </div>
                </ng-container>
              </th>

              <th class="background-white" id="addNewExpense">
                <ng-container *ngIf="allowExpenseSelection">
                  <div class="d-flex flex-nowrap flex-row">
                    <a href="javascript:;void" class="btn btn-icon btn-light btn-sm btn-hover-primary" (click)="addExpense()">
                      <span title="Add Expense" [inlineSVG]="'assets/media/svg/icons/color-save.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
                    </a>
                    <a title="Reset Filter" (click)="resetFilter()" class="ml-2 btn btn-icon btn-light btn-sm btn-hover-primary">
                      <span title="Reset Filter" [inlineSVG]="'assets/media/svg/icons/color-delete.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
                    </a>
                  </div>
                </ng-container>
              </th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-expense let-editing="editing" let-ri="rowIndex">
            <tr [pEditableRow]="expense">
              <td></td>
              <td>
                {{ expense?.key }}
              </td>
              <td pEditableColumn [pEditableColumnDisabled]="true" class="position-rel">
                <p-cellEditor>
                  <ng-template pTemplate="input">
                    <input
                      sflIsNumber
                      pInputText
                      class="editRow"
                      type="search"
                      (input)="expenseCostChange(ri)"
                      [(ngModel)]="editExpenseObject[ri].cost"
                      name="cost"
                      [ngModelOptions]="{ standalone: true }"
                    />
                    <small *ngIf="showCostError" class="form-text text-danger"> Cost is required </small>
                  </ng-template>
                  <ng-template pTemplate="output"> ${{ expense?.cost | addCommasToNumbers }} </ng-template>
                </p-cellEditor>
              </td>
              <td pEditableColumn [pEditableColumnDisabled]="true" class="position-rel">
                <p-cellEditor>
                  <ng-template pTemplate="input">
                    <input
                      pInputText
                      type="search"
                      class="editRow"
                      (input)="expenseDescChange(ri)"
                      [(ngModel)]="editExpenseObject[ri].description"
                      name="desc"
                      [ngModelOptions]="{ standalone: true }"
                    />
                    <small *ngIf="showDescriptionError" class="form-text text-danger"> Description is required </small>
                  </ng-template>
                  <ng-template pTemplate="output">
                    {{ expense?.description }}
                  </ng-template>
                </p-cellEditor>
              </td>
              <td>
                <p-cellEditor class="position-rel">
                  <ng-template pTemplate="input">
                    <p-dropdown
                      appendTo="body"
                      [options]="expense?.key === 'Daily' ? dailyExpenseTypes : monthlyExpenseTypes"
                      class="editRow"
                      [style]="{ width: '100%' }"
                      [(ngModel)]="editExpenseObject[ri].type_id"
                      [ngModelOptions]="{ standalone: true }"
                    >
                    </p-dropdown>
                  </ng-template>
                  <ng-template pTemplate="output">
                    {{ expense?.key === 'Daily' ? expense?.type?.name : expense?.expense_type?.name }}
                  </ng-template>
                </p-cellEditor>
              </td>
              <td pEditableColumn [pEditableColumnDisabled]="true" class="position-rel">
                <p-cellEditor>
                  <ng-template pTemplate="input" *ngIf="expense?.month">
                    <p-calendar
                      class="edit-cal"
                      appendTo="body"
                      view="month"
                      [(ngModel)]="editExpenseObject[ri].date"
                      dateFormat="MM/yy"
                      [yearNavigator]="true"
                      yearRange="2000:2030"
                      [readonlyInput]="true"
                      inputId="monthpicker"
                      placeholder="Month/Year"
                      [ngModelOptions]="{ standalone: true }"
                      [minDate]="minDate"
                      [maxDate]="maxDate"
                    >
                    </p-calendar>
                  </ng-template>
                  <ng-template pTemplate="output">
                    {{ expense?.month ? this.getMonthName(expense.month) + ' ' + expense?.year : ('' | uppercase) }}
                  </ng-template>
                </p-cellEditor>
              </td>
              <td>
                <div ngbDropdown class="d-inline-block" *ngIf="!editing" container="body">
                  <button class="btn btn-clean btn-sm btn-icon btn-icon-md btn-expand" id="dropdownBasic1" ngbDropdownToggle [disabled]="isEditExpense">
                    <em class="flaticon-more"></em>
                  </button>
                  <div ngbDropdownMenu aria-labelledby="dropdownBasic1" isOpen="false" placement="left">
                    <button ngbDropdownItem pInitEditableRow (click)="editExpense(expense, ri)">Edit</button>
                    <button ngbDropdownItem (click)="confirmDeleteExpense(expense)">Delete</button>
                  </div>
                </div>
                <button
                  *ngIf="editing"
                  pButton
                  pRipple
                  type="button"
                  icon="pi pi-check"
                  class="p-button-rounded p-button-text p-button-success p-mr-2"
                  (click)="saveEditExpense(ri)"
                ></button>
                <button
                  *ngIf="editing"
                  pCancelEditableRow
                  pButton
                  pRipple
                  type="button"
                  icon="pi pi-times"
                  class="p-button-rounded p-button-text p-button-danger"
                  (click)="cancelEditExpense(expense, ri)"
                ></button>
              </td>
            </tr>
          </ng-template>
          <ng-template pTemplate="emptymessage">
            <tr>
              <td colspan="12" class="center-align">No Expense found.</td>
            </tr>
          </ng-template>
        </p-table>
      </form>
    </div>
    <div class="overlay-layer" *ngIf="!hasPositionFlag">
      <div>
        <em class="fas fa-lock"></em>
      </div>
      <div class="text">You cannot add project level expenses until at least one position has been created.</div>
      <div class="text">
        <a href="javascript:;void" class="add-new" (click)="gotoNextTab()">Position Setup</a>
      </div>
    </div>
  </div>
</ng-template>

<p-dialog header="Delete Expense" [(visible)]="showExpenseDeleteDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0">Are you sure that you want to delete?</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeDeleteModal()">No</button>
      <button type="button" class="btn-save" (click)="deleteExpense()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog
  header="Adjust Revenue"
  [(visible)]="showRetainerPlugDialog"
  [modal]="true"
  class="retainer-plug-dialog lock-dialog"
  [baseZIndex]="10000"
  [draggable]="false"
  [resizable]="false"
  [style]="{ width: '30vw' }"
>
  <div *ngIf="retainerPlugs && retainerPlugs.length > 0" class="p-m-0 mt-1 mb-1">
    <div *isFetchingData="retainerPlugsLoading$">
      <h6 class="form-group first text text-wrap mb-2">
        Adjusting the revenue on a retainer project ensures that the recognized revenue for these months will not change regardless of project changes.
      </h6>

      <div class="form-group">
        <h5>Select Months to Update</h5>
        <p-multiSelect
          [options]="monthOptions"
          [(ngModel)]="selectedRetainerMonths"
          defaultLabel="Select months"
          optionLabel="label"
          [filter]="true"
          filterBy="label"
          display="chip"
          (onChange)="onMonthSelectionChange()"
          [maxSelectedLabels]="3"
          [selectedItemsLabel]="appConstants.selectedMonthsLabel"
          styleClass="full-width"
          appendTo="body"
        >
          <ng-template let-item pTemplate="item">
            <div class="month-option-item">
              <span>{{ item.label }}</span>
              <span class="month-option-amount">${{ item.data.amount | number : '1.2-2' }}</span>
            </div>
          </ng-template>
        </p-multiSelect>
      </div>

      <div class="form-group">
        <h5>Revenue Amount <span class="required-field">*</span></h5>
        <div class="p-inputgroup">
          <span class="p-inputgroup-addon">$</span>
          <p-inputNumber [min]="0" inputId="retainerAmount" [(ngModel)]="retainerAmount" name="amount" (onInput)="valueChange('amount')"></p-inputNumber>
        </div>
        <small *ngIf="showAmountError" class="form-text text-danger">Amount is required</small>
      </div>

      <div class="form-group">
        <h5>Note <span class="required-field">*</span></h5>
        <textarea
          rows="5"
          pInputTextarea
          [(ngModel)]="retainerReason"
          name="reason"
          (input)="valueChange('reason')"
          placeholder="Enter a reason for updating the revenue"
        ></textarea>
        <small *ngIf="showRetainerPlugError" class="form-text text-danger">Note is required</small>
      </div>
    </div>
  </div>
  <div *ngIf="!retainerPlugs || retainerPlugs.length === 0" class="p-m-0 mt-2 mb-2">
    <div class="alert alert-info">No months available for adjustment.</div>
  </div>

  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center" *isFetchingData="retainerPlugsLoading$">
      <button type="button" class="btn-cancel" (click)="closeRetainerPlugDialog()">Cancel</button>
      <button
        [disabled]="!this.selectedRetainerMonths?.length"
        type="button"
        class="btn-save"
        (click)="saveRetainerPlugs()"
        [isSubmitting]="isRetainerPlugSubmitting"
        *ngIf="retainerPlugs && retainerPlugs.length > 0"
      >
        Adjust Revenue
      </button>
    </div>
  </ng-template>
</p-dialog>
