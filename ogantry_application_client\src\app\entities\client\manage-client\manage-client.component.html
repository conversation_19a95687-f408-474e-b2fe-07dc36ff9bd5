<div class="card card-custom gutter-b" id="manageClient">
  <app-card-header [cardTitle]="cardTitle" [buttons]="buttons" [showSplitButton]="true" [splitButtonDropDownOption]="splitButtonDropDownOption"></app-card-header>
  <div>
    <div class="float-left py-2 px-2">
      <div class="d-inline ml-2" *ngIf="checkedClient?.length">
        <span title="Add Tag" [inlineSVG]="'assets/media/svg/icons/add-tag.svg'" cacheSVG="true" class="plus-icon pointer" (click)="addTagsToMultipleClient()"> </span>
      </div>
    </div>
    <span (clickOutside)="isShowHideColumns ? (isShowHideColumns = false) : ''">
      <div class="float-right mr-4 py-2 pointer" (click)="isShowHideColumns = !isShowHideColumns" #coulmnToggel>
        <app-filter-icon-shared></app-filter-icon-shared>
      </div>
      <div class="card popup-column">
        <app-filter-table-fields
          *ngIf="isShowHideColumns"
          [selectedColumns]="selectedColumns"
          [frozenCols]="frozenCols"
          dynamicBindingKey="monthLabel"
          (onSelectColumChange)="onSelectColumsChange($event)"
        ></app-filter-table-fields>
      </div>
    </span>
  </div>
  <div class="card-body">
    <p-table
      responsiveLayout="scroll"
      #dt
      [value]="customers"
      (onPage)="pageChange()"
      [lazy]="true"
      [resizableColumns]="true"
      [(selection)]="checkedClient"
      (onLazyLoad)="loadClient($event)"
      [rows]="10"
      [showCurrentPageReport]="true"
      [totalRecords]="totalRecords"
      [rowsPerPageOptions]="[10, 25, 50]"
      selectionMode="multiple"
      [loading]="loading"
      editMode="row"
      [scrollable]="true"
      [paginator]="loading ? false : showPaginator ? true : false"
      currentPageReportTemplate="Displaying {first} - {last} of {totalRecords} records"
      (onSort)="sortColumn()"
      [filterDelay]="0"
      [sortField]="sortFieldName"
      [sortOrder]="sortOrderNumber"
      (selectionChange)="listChecked()"
    >
      <ng-template pTemplate="header">
        <tr class="sticky-row-1">
          <th class="header-width-action text-center action-buttons d-flex justify-content-center" style="max-width: 42px !important">
            <div>
              <p-checkbox
                [(ngModel)]="listSelected"
                [binary]="true"
                checkboxIcon="pi pi-minus"
                (ngModelChange)="removeClient()"
                (ngModelChange)="listSelected ? selectAllClientCheck() : removeClient()"
              ></p-checkbox>
            </div>
          </th>
          <th [title]="_pCols" *ngIf="_pCols.includes('company')" id="company" pSortableColumn="name" class="header-width" pResizableColumn>
            Company<p-sortIcon field="name"></p-sortIcon>
          </th>
          <th *ngIf="_pCols.includes('primary_contact')" id="contactPerson" class="contact-person-header-width" pResizableColumn>Primary Contact</th>
          <th *ngIf="_pCols.includes('phone')" id="phone" class="header-width phoneNumber-col-wrapper phoneNumber-col-wrapper" pResizableColumn>Phone</th>
          <th *ngIf="_pCols.includes('email')" id="email" class="header-width" pResizableColumn>Email</th>
          <th *ngIf="_pCols.includes('status')" id="status" pSortableColumn="is_active" class="header-width" pResizableColumn>Status<p-sortIcon field="is_active"></p-sortIcon></th>
          <th *ngIf="_pCols.includes('tags')" id="tags" class="tags-col-wrapper">Tags</th>

          <ng-container *ngFor="let item of extendFields; let index = index">
            <ng-container *ngFor="let filed of item?.jsonData?.extendedFieldsConfig; let index2 = index">
              <ng-container *ngIf="filed?.component == componentType">
                <ng-container *ngFor="let filedDetails of filed?.fields; let index3 = index">
                  <th colspan="1" *ngIf="checkSelectedColumn(filedDetails?.name)">
                    {{ filedDetails?.name }}
                  </th>
                </ng-container>
              </ng-container>
            </ng-container>
          </ng-container>

          <th id="actions" class="action-col-wrapper text-center justify-content-center">
            <ng-container class="d-flex">
              <div class="column-action-wrapper">Actions</div>
            </ng-container>
          </th>
        </tr>
        <tr class="sticky-row-2" *ngIf="showFilter">
          <th class="d-flex justify-content-center" style="max-width: 42px !important"></th>
          <th id="searchCompany" *ngIf="_pCols.includes('company')">
            <span class="p-input-icon-right">
              <em class="pi pi-times" *ngIf="dataFilter.name_search" (click)="clearCompanyFilter()"></em>
              <input pInputText type="text" [(ngModel)]="dataFilter.name_search" (input)="filter()" class="p-column-filter" />
            </span>
          </th>
          <th id="searchPerson" *ngIf="_pCols.includes('primary_contact')"></th>
          <th id="searchPhone" *ngIf="_pCols.includes('phone')" class="phoneNumber-col-wrapper"></th>
          <th id="searchEmail" *ngIf="_pCols.includes('email')"></th>
          <th id="searchStatus" *ngIf="_pCols.includes('status')">
            <p-dropdown
              class="search-status-wrapper"
              appendTo="body"
              [options]="statuses"
              [(ngModel)]="dataFilter.is_active"
              (onChange)="filter()"
              styleClass="p-column-filter pi-icon"
              placeholder="Status"
            >
            </p-dropdown>
          </th>
          <th *ngIf="_pCols.includes('tags')" id="filter-tag" class="tags-col-wrapper">
            <p-treeSelect
              class="search-tag-wrapper"
              [(ngModel)]="selectedTags"
              (ngModelChange)="tagSelected($event)"
              [options]="groupedCategory?.data"
              display="chip"
              [metaKeySelection]="false"
              selectionMode="checkbox"
              placeholder="Select Tags"
              [ngModelOptions]="{ standalone: true }"
              filterBy="label"
            >
            </p-treeSelect>
          </th>

          <ng-container *ngFor="let item of extendFields; let index = index">
            <ng-container *ngFor="let filed of item?.jsonData?.extendedFieldsConfig; let index2 = index">
              <ng-container *ngIf="filed?.component == componentType">
                <ng-container *ngFor="let filedDetails of filed?.fields; let index3 = index">
                  <th colspan="1" *ngIf="checkSelectedColumn(filedDetails?.name)" [id]="filedDetails?.name">
                    <span class="p-input-icon-right" *ngIf="filedDetails?.DBTag">
                      <em class="pi pi-times" *ngIf="getFilterValue(filedDetails?.DBTag)" (click)="deleteExtendFiledFilter(filedDetails?.DBTag)"></em>
                      <input
                        pInputText
                        type="text"
                        [value]="getFilterValue(filedDetails?.DBTag)"
                        (input)="filterExtendFiled(filedDetails?.DBTag, $event?.target)"
                        class="p-column-filter"
                        [placeholder]="filedDetails?.name"
                      />
                    </span>
                  </th>
                </ng-container>
              </ng-container>
            </ng-container>
          </ng-container>

          <th class="justify-content-center action-col-wrapper"></th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-customer>
        <tr>
          <td class="d-flex justify-content-center" style="max-width: 42px !important; height: 40px">
            <p-tableCheckbox [value]="customer"></p-tableCheckbox>
          </td>
          <td *ngIf="_pCols.includes('company')">
            <span [title]="customer?.customer?.name">
              {{ customer?.customer?.name }}
            </span>
          </td>
          <td *ngIf="_pCols.includes('primary_contact')">
            <span class="image-text">{{ getContactInfo(customer?.customer?.contacts, 'name') }}</span>
          </td>
          <td *ngIf="_pCols.includes('phone')" class="phoneNumber-col-wrapper">
            {{ getContactInfo(customer?.customer?.contacts, 'phone') }}
          </td>
          <td *ngIf="_pCols.includes('email')">
            {{ getContactInfo(customer?.customer?.contacts, 'email') }}
          </td>
          <td *ngIf="_pCols.includes('status')">
            <span class="image-text" class="badge badge-inactive" [ngClass]="{ 'badge-active': customer?.customer.is_active }">
              {{ customer?.customer.is_active ? 'Active' : 'Inactive' }}</span
            >
          </td>
          <td class="header-width tags-td tags-col-wrapper" *ngIf="_pCols.includes('tags')">
            <ng-container *ngIf="customer?.customer?.tags.length">
              <span class="ellipses">
                <span class="taglist">
                  <p-chip *ngFor="let tag of customer?.customer?.tags; let i = index" (click)="openTagModal(customer?.customer?.tags)" class="cursor-pointer">
                    <span class="tooltip-hover" [ngbTooltip]="categoryDetails" #t2="ngbTooltip" (mouseenter)="toggleWithCategory(t2, tag)">{{ getTagsCount(tag, i < 2) }}</span>
                  </p-chip>
                </span>
              </span>
              <span class="count cursor-pointer" *ngIf="customer?.customer?.tags.length > 2" (click)="openTagModal(customer?.customer?.tags)">
                <span class="tag-count">
                  <p-badge [value]="getTagCount(customer?.customer?.tags)"></p-badge>
                </span>
              </span>
            </ng-container>
          </td>

          <ng-container *ngFor="let item of extendFields; let index = index">
            <ng-container *ngFor="let filed of item?.jsonData?.extendedFieldsConfig; let index2 = index">
              <ng-container *ngIf="filed?.component == componentType">
                <ng-container *ngFor="let filedDetails of filed?.fields; let index3 = index">
                  <td
                    class="show-pointer"
                    colspan="1"
                    *ngIf="checkSelectedColumn(filedDetails?.name)"
                    [id]="filedDetails?.name"
                    (click)="openExtendFiledPopup(customer, filedDetails?.name)"
                    [ngClass]="{ 'dynamic-text text-truncate': filedDetails.type === filedType.Text_Area }"
                  >
                    {{ getValueByPartialKey(filedDetails?.DBTag, customer?.customer?.extended_fields) }}
                  </td>
                </ng-container>
              </ng-container>
            </ng-container>
          </ng-container>

          <td class="text-center pr-0 d-flex justify-content-center action-col-wrapper" colspan="3">
            <a
              class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary"
              *hasAnyPermission="permissionModules.MANAGE_CLIENT; disableEvent: true"
              [routerLink]="[appRoutes.EDIT_CLIENT, customer?.customer?.id]"
            >
              <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
            </a>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="6" class="center-align">No Clients found.</td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>

<ng-template #categoryDetails let-tag="tag">
  <p [innerHTML]="getTagCategorySubCategory(tag)"></p>
</ng-template>

<p-dialog header="Applied Tags" [(visible)]="showTagDialog" [modal]="true" class="dialog-applied-tags" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <ul>
    <li *ngFor="let tag of selectedTagToView">
      <span [ngbTooltip]="categoryDetails" #t3="ngbTooltip" (mouseenter)="toggleWithCategory(t3, tag)">{{ getExtractedTags(tag) }}</span>
    </li>
  </ul>
</p-dialog>

<p-dialog header="Rename" [(visible)]="showEditDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <div class="form-group first" *ngIf="editFilterObj">
    <input type="text" pInputText class="form-control custom" [(ngModel)]="editFilterObj.query_filter.name" (input)="inputFilterName()" />
    <small *ngIf="showNameError" class="form-text text-danger"> Name is required</small>
  </div>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">Cancel</button>
      <button type="button" class="btn-save" (click)="saveEditFilter()" [isSubmitting]="isSubmitting">Update</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog header="Delete" [(visible)]="showDeleteDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0 delete-title">Do you want to delete "{{ deleteFilterObj?.query_filter?.name }}" Filter?</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="saveDeleteFilter()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog [header]="shareFilterObj?.header" [(visible)]="showShareDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0 delete-title">{{ shareFilterObj?.text }}</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="saveShareFilter()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog
  dismissableMask="true"
  backdrop="false"
  position="top-right"
  [(visible)]="showSavedFilter"
  [modal]="true"
  class="filter-dialog-manage-client"
  [draggable]="false"
  [resizable]="false"
>
  <ng-template pTemplate>
    <div class="filter-listing">
      <input type="search" class="form-control mb-2" placeholder="Search" (input)="searchFilters($event.target.value)" />
      <div *hasAnyPermission="permissionModules.VIEW_SHARE_FILTER; hideTemplate: true" class="title">Shared Filters</div>
      <ng-container *hasAnyPermission="permissionModules.VIEW_SHARE_FILTER; hideTemplate: true">
        <span *ngIf="sharedFilters?.length; else noData">
          <div
            class="form-check filter-body"
            *ngFor="let filterOption of sharedFilters"
            [ngClass]="{
              'selected-filter': filterOption?.query_filter?.id === selectedFilterFormControl?.value?.query_filter?.id
            }"
          >
            <label class="form-check-label">
              <input
                [formControl]="selectedFilterFormControl"
                (ngModelChange)="applySelectedFilterAndUpdateUrl()"
                type="radio"
                class="form-check-input custom-radio"
                [value]="filterOption"
                name="filteroption"
              />{{ filterOption?.query_filter?.name }}
            </label>
            <div class="filter-icons">
              <ng-container *hasAnyPermission="permissionModules.MANAGE_SHARE_FILTER; hideTemplate: true">
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary copy-icon" (click)="copyLinkToTheFilter(filterOption?.query_filter?.id)">
                  <em title="Copy Filter Link" class="fa-regular fa-copy"></em>
                </a>
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary share-icon" (click)="unShareFilter(filterOption)">
                  <em class="fa-solid fa-share" title="UnShare Filter"></em>
                </a>
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
                  <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="editFilter(filterOption)"> </span>
                </a>
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
                  <span title="Delete" [inlineSVG]="'assets/media/svg/icons/delete.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="deleteFilter(filterOption)"> </span>
                </a>
              </ng-container>
            </div>
          </div>
        </span>
      </ng-container>

      <ng-template #noData>
        <div>-</div>
      </ng-template>
      <div class="title">My Filters</div>
      <span *ngIf="myFilters?.length; else noData">
        <div
          class="form-check filter-body"
          *ngFor="let filterOption of myFilters"
          [ngClass]="{
            'selected-filter': filterOption?.query_filter?.id === selectedFilterFormControl?.value?.query_filter?.id
          }"
        >
          <label class="form-check-label">
            <input
              [formControl]="selectedFilterFormControl"
              (ngModelChange)="applySelectedFilterAndUpdateUrl()"
              type="radio"
              class="form-check-input custom-radio"
              [value]="filterOption"
              name="filteroption"
            />{{ filterOption?.query_filter?.name }}
          </label>
          <div class="filter-icons">
            <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary copy-icon" (click)="copyLinkToTheFilter(filterOption?.query_filter?.id)">
              <em title="Copy Filter Link" class="fa-regular fa-copy"></em>
            </a>
            <a
              class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary"
              (click)="shareFilter(filterOption)"
              *hasAnyPermission="permissionModules.MANAGE_SHARE_FILTER; hideTemplate: true"
            >
              <em class="fa-solid fa-share" title="Share Filter"></em>
            </a>
            <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
              <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="editFilter(filterOption)"> </span>
            </a>
            <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
              <span title="Delete" [inlineSVG]="'assets/media/svg/icons/delete.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="deleteFilter(filterOption)"> </span>
            </a>
          </div>
        </div>
      </span>
    </div>
  </ng-template>
</p-dialog>

<p-dialog
  [header]="UPDATE + updateExtendFiled"
  [(visible)]="showUpdateExtendFiledDialog"
  [modal]="true"
  class="description-dialog"
  [baseZIndex]="10000"
  [draggable]="false"
  [resizable]="false"
  [style]="{ width: '40vw' }"
>
  <app-extended-form [extendFieldsObj]="clientObj?.customer?.extended_fields" [filedName]="updateExtendFiled" [componentType]="componentType"> </app-extended-form>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeExtendFiledPopup()">Cancel</button>
      <button type="button" class="btn-save" (click)="saveClient()" [isSubmitting]="isSubmitting">Save</button>
    </div>
  </ng-template>
</p-dialog>
