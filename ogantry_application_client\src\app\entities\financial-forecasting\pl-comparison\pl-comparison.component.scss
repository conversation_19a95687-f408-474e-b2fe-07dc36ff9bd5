@import '../../../../../src/assets/sass/pages/wizard/wizard4';
@import '../../../../../src/assets/sass/layout/variables';
#comparePL {
  ::ng-deep .p-treetable .p-treetable-thead > tr > th.header-width {
    width: 14%;
  }

  ::ng-deep .p-treetable .p-treetable-thead > tr > th {
    height: 20px;
    color: #4b3f72;
    font-family: Poppins;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 21px;
    border-bottom: none;
    width: 75px;
    padding: 0.5rem 0.5rem;
    background-color: #ecedf6 !important;
  }

  .p-r-15 {
    padding-right: 15px !important;
  }

  .card.card-custom {
    height: 100%;
    box-shadow: none;
    border: 0;
  }

  .card-shadowless {
    overflow-x: hidden;
    overflow-y: hidden !important;
  }

  ::ng-deep .card-header-wrapper .card-header {
    border-bottom: none !important;
  }

  .card-body .wizard-nav {
    border-top: 1px solid #ebedf2;
  }

  ::ng-deep .p-treetable .p-treetable-thead > tr > th:nth-child(2) {
    width: 90px;
  }
  ::ng-deep .wizard-desc {
    height: 15px;
    color: #7c7c7c;
    font-family: Poppins;
    font-size: 10px;
    letter-spacing: 0;
    line-height: 16px;
  }
  ::ng-deep .wizard-steps {
    justify-content: space-between;
  }

  ::ng-deep .p-treetable .p-treetable-tbody > tr > td {
    height: 20px;
    width: 123px;
    color: #000000;
    font-family: Poppins;
    font-size: 12px;
    letter-spacing: 0;
    line-height: 15px;
    font-weight: 500;
    padding: 0.5rem 0.5rem;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  ::ng-deep .p-treetable .p-treetable-tbody > tr > td .toggleIcon {
    display: inherit;
  }

  ::ng-deep .p-treetable-tbody tr:last-child > td {
    border-bottom: none;
  }

  ::ng-deep .negative-bench-tooltip {
    .tooltip-inner {
      background: #4b3f72 !important;
      white-space: normal;
      color: #ffffff;
      padding: 0.5rem 0.5rem;
      box-shadow: 0 2px 4px -1px rgb(0 0 0 / 20%), 0 4px 5px 0 rgb(0 0 0 / 14%), 0 1px 10px 0 rgb(0 0 0 / 12%);
      border-radius: 3px;
      font-weight: 500;
      text-align: left;
    }
    .arrow::before {
      border-top-color: #4b3f72 !important;
    }
  }

  .font-weight-bold {
    font-weight: 600 !important;
  }

  .children-level-1 {
    color: #252525;
    font-weight: 500;
  }

  ::ng-deep .pi {
    font-weight: bolder;
  }
  ::ng-deep i,
  ::ng-deep i {
    color: #4b3f72;
  }

  .center-align {
    text-align: center !important;
  }

  ::ng-deep .p-treetable .p-treetable-frozen-view .p-treetable-scrollable-header .p-treetable-thead > tr > th {
    visibility: hidden;
  }

  ::ng-deep .p-treetable .p-treetable-frozen-view .p-treetable-scrollable-header-table {
    background-color: #ecedf6 !important;
  }

  ::ng-deep table.p-treetable-scrollable-header-table .p-treetable-thead > tr > th {
    text-align: right;
  }
  ::ng-deep .p-treetable-scrollable-view.p-treetable-unfrozen-view .p-treetable-scrollable-body .p-treetable-tbody > tr > td {
    text-align: right;
  }

  .scrollable-content {
    max-height: calc((var(--fixed-content-height, 1vh) * 100) - 100px) !important;
    overflow: hidden !important;
  }

  ::ng-deep .p-treetable-unfrozen-view .p-treetable-scrollable-body {
    overflow-y: auto !important;
  }

  ::ng-deep .p-treetable-scrollable-wrapper {
    overflow-y: auto;
    max-height: calc((var(--fixed-content-height, 1vh) * 100) - 125px);
  }

  ::ng-deep .p-treetable-scrollable-header {
    position: sticky;
    position: -webkit-sticky;
    z-index: 1;
    top: 0;
  }
  ::ng-deep .green {
    color: #3bb950;
    i {
      color: #3bb950;
      vertical-align: -webkit-baseline-middle;
    }
  }
  ::ng-deep .red {
    color: #b93b3b;
    i {
      color: #b93b3b;
      vertical-align: -webkit-baseline-middle;
    }
  }
}

.form-group {
  padding-bottom: 0;
  &:first-child {
    padding-top: 0;
  }
}
::ng-deep .dropdown .p-dropdown,
::ng-deep .dropdown .p-dropdown .p-focus {
  width: 100%;
  height: 100%;
  border-radius: 9px !important;
  border: none !important;
  box-shadow: none !important;
  background-color: #f8f8ff !important;
  min-height: 60px !important;
  padding: 1.2rem;
}

::ng-deep .dropdown .p-dropdown .p-dropdown-label {
  color: #000000;
  font-family: Poppins;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: -0.32px;
  line-height: 25px;
}

::ng-deep .dropdown .p-dropdown .p-dropdown-label.p-placeholder {
  color: #b5b5c3 !important;
}

.pointer {
  cursor: pointer;
}
::ng-deep .p-datatable .p-datatable-loading-overlay {
  top: 29px;
  background-color: transparent;
}

::ng-deep .custom-pop.popover {
  max-width: 100% !important;
  min-width: 500px;
  min-height: 160px !important;
  max-height: 350px !important;
  overflow: auto !important;
  margin-bottom: 0rem;
  .arrow {
    bottom: 0rem !important;
    right: 0rem !important;
  }
}
::ng-deep .custom-pop-compare.popover {
  width: 20%;
  height: 15%;
  opacity: 0.95;
  border-radius: 2px;
  background-color: #4b3f72;
  .color-white {
    height: 0px;
    text-align: center;
    color: #fdfdfd;
    font-family: Poppins;
    font-size: 12px;
    letter-spacing: 0;
    line-height: 18px;
  }
}
::ng-deep .p-datatable .p-datatable-thead tr > th:first-child,
::ng-deep .p-datatable .p-datatable-tbody tr > td:first-child {
  padding-left: 1rem;
}

::ng-deep .p-datatable .p-datatable-thead > tr > th {
  height: 20px;
  color: #4b3f72;
  font-family: Poppins;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 21px;
  border-bottom: none;
  padding: 0.5rem 0.5rem;
  background-color: #ecedf6 !important;
}
.text-underline {
  text-decoration: underline;
  color: #4b3f72;
}
::ng-deep .p-datatable .p-datatable-tbody > tr > td {
  height: 20px;
  width: 123px;
  color: #000000;
  font-family: Poppins;
  font-size: 12px;
  letter-spacing: 0;
  line-height: 15px;
  padding: 0.5rem 0.5rem;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
}

.confirm-dialog {
  ::ng-deep .p-dialog {
    width: 30vw;
  }

  ::ng-deep .p-inputtextarea,
  ::ng-deep .p-inputtextarea:focus {
    height: 102.79px;
    width: 296.1px;
    border-radius: 9px;
    background-color: #f8f8ff;
    border: none;
  }
  ::ng-deep .p-inputgroup {
    border-radius: 9px !important;
    border: none !important;
    background-color: #f8f8ff !important;
    height: 60px;
    width: 267.36px;
  }

  ::ng-deep .p-inputgroup .p-inputtext {
    border: none !important;
    background-color: #f8f8ff !important;
    padding-left: 1rem;
  }

  ::ng-deep .p-inputgroup-addon {
    border: 0 !important;
    width: 4rem;
  }

  ::ng-deep .p-inputtext:enabled:focus {
    box-shadow: none;
  }
}
::ng-deep .filter-dialog {
  .p-component-overlay {
    background: none !important;
    animation: none !important;
  }
  .p-dialog {
    height: 253px;
    width: 400px;
    top: 51px;
    .p-dialog-header {
      display: none;
    }

    .p-dialog-content {
      padding-top: 1rem;
    }
    .title {
      color: #757575;
      font-family: Poppins;
      font-size: 12px;
      letter-spacing: 0;
      line-height: 18px;
    }
    .share-icon {
      background-color: #4b3f72 !important;
      border-color: #4b3f72 !important;
      i,
      em {
        color: white;
      }
    }
    .filter-body {
      display: flex;
      align-content: center;
      align-items: center;
    }
    .form-check {
      height: 40px;
      background-color: white;
      display: flex;
      align-items: center;
      width: 100%;
      justify-content: space-between;
      .form-check-label,
      .form-check-label:hover {
        cursor: pointer;
        color: black;
        color: #000000;
        font-family: Poppins;
        font-size: 12px;
        letter-spacing: 0;
        line-height: 18px;
        width: 140px;
      }
      .div {
        width: 30%;
        display: flex;
        justify-content: flex-end;
      }
    }

    .filter-icons {
      display: flex;
      width: 100%;
      justify-content: flex-end;
      a {
        margin: 0 3px;
      }
    }
  }
}
::ng-deep .p-tooltip {
  max-width: 100%;
  width: 32rem;
}
::ng-deep .p-tooltip .p-tooltip-text {
  background: #4b3f72 !important;
  padding: 1.5rem;
  white-space: normal;
  border-radius: 2px;
  .row {
    padding: 0.6rem 0;
  }
}

.paused-project-name {
  color: $primary;
}

.recalculating-message {
  display: flex;
  align-items: center;
  color: $primary;
  font-weight: 500;
  animation: pulse 1.5s infinite;
}

.rotating-icon {
  display: inline-block;
  animation: rotate 3s infinite linear;
  color: $primary;
  font-size: 16px;
  vertical-align: middle;
}

::ng-deep .p-component-overlay .p-dialog .p-dialog-content .paused-project-table .p-datatable-wrapper {
  max-height: 250px !important;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@media (max-width: 767px) {
  .wizard-steps .wizard-step {
    margin-bottom: 0 !important;
    padding: 5px 0 0 !important;
  }
  .start-date {
    margin-left: 0 !important;
  }
}
