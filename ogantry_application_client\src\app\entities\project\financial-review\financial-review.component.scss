#financialReview {
  ::ng-deep .p-treetable .p-treetable-thead > tr > th.header-width {
    width: 14%;
  }

  ::ng-deep .p-treetable .p-treetable-thead > tr > th.month-width {
    width: 15%;
  }

  ::ng-deep .p-treetable .p-treetable-thead > tr > th.total-width {
    width: 15%;
  }

  ::ng-deep .p-treetable .p-treetable-thead > tr > th {
    height: 20px;
    color: #4b3f72;
    font-family: Poppins;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 21px;
    border-bottom: none;
    padding: 0.5rem 0.5rem;
    width: 75px;
    background-color: #ecedf6 !important;
  }
  ::ng-deep .p-treetable .p-treetable-frozen-view .p-treetable-scrollable-header-table {
    background-color: #ecedf6 !important;
  }
  ::ng-deep .p-treetable .p-treetable-unfrozen-view .p-treetable-scrollable-header-table {
    background-color: #ecedf6 !important;
  }

  ::ng-deep .p-treetable .p-treetable-thead > tr > th:nth-child(2) {
    width: 90px;
  }

  ::ng-deep .p-treetable .p-treetable-tbody > tr > td {
    height: 20px;
    width: 250px;
    color: #000000;
    font-family: Poppins;
    font-size: 12px;
    letter-spacing: 0;
    line-height: 15px;
    padding: 0.5rem 0.5rem;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  ::ng-deep .p-treetable-tbody tr:last-child > td {
    border-bottom: none;
  }

  .font-weight-bold {
    font-weight: 600 !important;
  }

  .children-level-1 {
    color: #252525;
    font-weight: 500;
  }

  ::ng-deep .pi {
    font-weight: bolder;
  }
  ::ng-deep i,
  ::ng-deep i {
    color: #4b3f72;
  }

  ::ng-deep .p-treetable-flex-scrollable {
    height: calc(100% - -21px) !important;
  }

  ::ng-deep .p-treetable .p-treetable-frozen-view .p-treetable-scrollable-header .p-treetable-thead > tr > th {
    visibility: hidden;
  }

  ::ng-deep .p-treetable .p-treetable-frozen-view .p-treetable-scrollable-body .p-treetable-tbody > tr > .retrieving-msg {
    visibility: hidden;
  }

  .scrollable-content {
    max-height: calc((var(--fixed-content-height, 1vh) * 100) - 100px) !important;
    overflow: hidden !important;
  }

  .underline {
    text-decoration: underline;
    color: #4b3f72;
  }

  ::ng-deep .p-treetable-unfrozen-view .p-treetable-scrollable-body {
    overflow-y: auto !important;
  }
  ::ng-deep table.p-treetable-scrollable-header-table .p-treetable-thead > tr > th {
    text-align: right;
  }
  ::ng-deep .p-treetable-scrollable-view.p-treetable-unfrozen-view .p-treetable-scrollable-body .p-treetable-tbody > tr > td {
    text-align: right;
  }
  ::ng-deep .p-treetable-scrollable-header {
    position: sticky;
    position: -webkit-sticky;
    z-index: 1;
    top: 0;
  }

  ::ng-deep .p-treetable-scrollable-wrapper {
    overflow-y: auto;
    max-height: calc((var(--fixed-content-height, 1vh) * 100) - 125px);
  }
  .cursor-pointer {
    cursor: pointer;
  }
}

.retainer-plug-wrapper {
  cursor: pointer;
  text-decoration: underline;
  color: #4b3f72;
}

.lock-dialog {
  ::ng-deep .p-dialog {
    width: 30vw;
  }
  .form-group {
    padding-bottom: 0;
    &:first-child {
      padding-top: 0;
    }
  }
  .text {
    color: #6f6f6f;
    font-family: Poppins;
    font-size: 12px;
    letter-spacing: 0;
    line-height: 18px;
  }
  ::ng-deep .p-inputgroup {
    border-radius: 9px !important;
    border: none !important;
    background-color: #f8f8ff !important;
    height: 60px;
    width: 267.36px;
  }

  ::ng-deep .p-inputgroup .p-inputtext {
    border: none !important;
    background-color: #f8f8ff !important;
    padding-left: 1rem;
  }

  ::ng-deep .p-inputgroup-addon {
    border: 0 !important;
    width: 4rem;
  }

  ::ng-deep .p-inputtext:enabled:focus {
    box-shadow: none;
  }
  ::ng-deep .p-inputtextarea,
  ::ng-deep .p-inputtextarea:focus {
    height: 102.79px;
    width: 296.1px;
    border-radius: 9px;
    background-color: #f8f8ff;
    border: none;
  }
}

@media (max-width: 500px) {
  ::ng-deep .p-treetable table {
    display: block;
    overflow: auto;
    white-space: nowrap;
  }
}

.icon-background {
  background-color: #4b3f72 !important;
  padding: 0.2rem;
}
