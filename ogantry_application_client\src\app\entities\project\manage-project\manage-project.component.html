<div class="card card-custom gutter-b" id="manageProject">
  <app-card-header
    [cardTitle]="cardTitle"
    [cardSubTitle]="cardSubTitle"
    [buttons]="buttons"
    [showSplitButton]="true"
    [splitButtonDropDownOption]="splitButtonDropDownOption"
  ></app-card-header>
  <div>
    <div class="float-left py-2 px-2">
      <div class="d-inline ml-2" *ngIf="checkedProject?.length">
        <span title="Add Tag" [inlineSVG]="'assets/media/svg/icons/add-tag.svg'" cacheSVG="true" class="plus-icon pointer" (click)="addTagsToMultipleProject()"> </span>
      </div>
    </div>
    <span (clickOutside)="isShowHideColumns ? (isShowHideColumns = false) : ''">
      <div class="float-right mr-4 py-2 pointer" (click)="isShowHideColumns = !isShowHideColumns" #coulmnToggel>
        <app-filter-icon-shared></app-filter-icon-shared>
      </div>
      <div class="card popup-column">
        <app-filter-table-fields
          *ngIf="isShowHideColumns"
          [selectedColumns]="selectedColumns"
          [frozenCols]="frozenCols"
          dynamicBindingKey="monthLabel"
          (onSelectColumChange)="onSelectColumsChange($event)"
        ></app-filter-table-fields>
      </div>
    </span>
  </div>
  <p-table
    [sortField]="sortFieldName"
    [resizableColumns]="true"
    [sortOrder]="sortOrderNumber"
    [(selection)]="checkedProject"
    [lazy]="true"
    [style]="{ overflow: 'auto!important' }"
    (onSort)="sortColumn()"
    [columns]="frozenCols"
    #dt
    [value]="projects"
    (onLazyLoad)="loadProject($event)"
    [scrollHeight]="height"
    editMode="row"
    [rows]="10"
    (onPage)="pageChange()"
    [paginator]="loading ? false : showPaginator ? true : false"
    [scrollable]="true"
    selectionMode="multiple"
    currentPageReportTemplate="Displaying {first} - {last} of {totalRecords} records"
    [rowsPerPageOptions]="[10, 25, 50]"
    [loading]="loading"
    [totalRecords]="totalRecords"
    responsiveLayout="scroll"
    (selectionChange)="listChecked()"
  >
    <ng-template pTemplate="header">
      <tr class="sticky-row-1">
        <th class="header-width-action text-center action-buttons d-flex justify-content-center" style="max-width: 42px !important">
          <div>
            <p-checkbox
              [(ngModel)]="listSelected"
              [binary]="true"
              checkboxIcon="pi pi-minus"
              (ngModelChange)="removeProject()"
              (ngModelChange)="listSelected ? selectAllProjectCheck() : removeProject()"
            ></p-checkbox>
          </div>
        </th>
        <th id="name" *ngIf="_pCols.includes('name')" pSortableColumn="customer_name" class="customer-width" pResizableColumn>
          Client<p-sortIcon field="customer_name"></p-sortIcon>
        </th>
        <th id="project_name" *ngIf="_pCols.includes('project_name')" class="project-width" pSortableColumn="name" pResizableColumn>
          Project<p-sortIcon field="name"></p-sortIcon>
        </th>
        <th id="start_date" *ngIf="_pCols.includes('start_date')" class="filter-header-width start-end-width" pResizableColumn pSortableColumn="start_date">
          Start Date
          <p-sortIcon field="start_date"></p-sortIcon>
        </th>
        <th id="end_date" *ngIf="_pCols.includes('end_date')" class="filter-header-width start-end-width" pResizableColumn pSortableColumn="end_date">
          End Date <p-sortIcon field="end_date"></p-sortIcon>
        </th>
        <th id="duration" *ngIf="_pCols.includes('duration')" class="filter-header-width duration-width" pResizableColumn>Duration</th>
        <th id="bill_type" *ngIf="_pCols.includes('bill_type')" pSortableColumn="billing_type" class="filter-header-width filter-header-status" pResizableColumn>
          Bill Type<p-sortIcon field="billing_type"></p-sortIcon>
        </th>
        <th id="status" *ngIf="_pCols.includes('status')" pSortableColumn="status" class="filter-header-width status filter-header-status" pResizableColumn>
          Status<p-sortIcon field="status"></p-sortIcon>
        </th>
        <th id="description" *ngIf="_pCols.includes('description')">Description</th>
        <th id="tags" *ngIf="_pCols.includes('tags')" class="filter-header-width tags-width">Tags</th>
        <!-- <ng-container *ngFor="let global of globalObject">
          <th [id]="global">{{ global }}</th>
        </ng-container> -->

        <ng-container *ngFor="let item of extendFields; let index = index">
          <!-- <p>{{ item.id }}</p> -->
          <ng-container *ngFor="let filed of item?.jsonData?.extendedFieldsConfig; let index2 = index">
            <ng-container *ngIf="filed?.component == componentType.Project || filed?.component == componentType.Client">
              <ng-container *ngFor="let filedDetails of filed?.fields; let index3 = index">
                <th colspan="1" *ngIf="checkSelectedColumn(filed?.component, filedDetails?.name)">
                  <span> {{ filed?.component }}: {{ filedDetails?.name | titlecase }} </span>
                </th>
                <!-- <input
              type="text"
              class="form-control custom mb-2"
              required
              [value]="getValueByPartialKey(filedDetails?.name)"
              (change)="
                updateObjectByPartialKey(
                  filedDetails?.name,
                  $event.target.value
                )
              "
              (focus)="
                updateObjectByPartialKey(
                  filedDetails?.name,
                  $event.target.value
                )
              "
              [placeholder]="filedDetails?.name"
            /> -->
              </ng-container>
            </ng-container>
          </ng-container>
        </ng-container>

        <th id="actions" class="action-width text-center d-flex justify-content-center">
          <ng-container class="d-flex">
            <div class="column-action-wrapper">Actions</div>
          </ng-container>
        </th>
      </tr>
      <tr class="sticky-row-2" *ngIf="showFilter">
        <th class="d-flex justify-content-center" style="max-width: 42px !important"></th>
        <th id="searchClient" *ngIf="_pCols.includes('name')" class="customer-width">
          <span class="p-input-icon-right">
            <em class="pi pi-times" *ngIf="dataFilter.customer_search" (click)="clearFilter('customer_search')"></em>
            <input pInputText type="text" [(ngModel)]="dataFilter.customer_search" (input)="filter()" class="p-column-filter" />
          </span>
        </th>
        <th id="searchCompany" *ngIf="_pCols.includes('project_name')" class="project-width">
          <span class="p-input-icon-right">
            <em class="pi pi-times" *ngIf="dataFilter.name_search" (click)="clearFilter('name_search')"></em>
            <input pInputText type="text" [(ngModel)]="dataFilter.name_search" (input)="filter()" class="p-column-filter" />
          </span>
        </th>
        <th id="searchDate" *ngIf="_pCols.includes('start_date')" class="start-end-width py-1">
          <div class="p-inputgroup h-100">
            <span *ngIf="dataFilter.start_date" class="p-inputgroup-addon">{{ getSymbol(dataFilter.selectedFilterStartDate) }}</span>
            <span class="p-input-icon-right cal-drop position-relative w-100 h-100" [ngClass]="dataFilter.start_date ? 'date-filter' : ''">
              <p-calendar
                #startCal
                appendTo="body"
                (onClose)="showCal ? startDateFilterSelected() : null"
                [(ngModel)]="dataFilter.start_date"
                showButtonBar="true"
                (onClearClick)="clearStartDate()"
                (onInput)="filterDate()"
                (onSelect)="onSelectDate(startDateKey)"
                (onTodayClick)="onTodayClicked(startDateKey)"
                inputStyleClass="p-0"
                class="position-absolute opacity-0 d-flex"
              >
                <ng-template pTemplate="header">
                  <span class="cal-drop">
                    <p-dropdown [(ngModel)]="dataFilter.selectedFilterStartDate" [options]="dateFilter"></p-dropdown>
                  </span>
                </ng-template>
              </p-calendar>
              <input
                pInputText
                type="text"
                [ngModel]="getFormattedFilterDate(startDateKey)"
                (click)="startCal.toggle()"
                class="p-column-filter position-absolute h-100 w-100 pr-2"
              />
            </span>
            <span *ngIf="dataFilter.start_date" class="p-inputgroup-cancel"><em class="pi pi-times" (click)="onClearDateFilter(startDateKey)"></em></span>
          </div>
        </th>
        <th id="endDate" *ngIf="_pCols.includes('end_date')" class="start-end-width py-1">
          <div class="p-inputgroup h-100">
            <span *ngIf="dataFilter.end_date" class="p-inputgroup-addon">{{ getSymbol(dataFilter.selectedFilterEndDate) }}</span>
            <span class="p-input-icon-right cal-drop position-relative w-100 h-100" [ngClass]="dataFilter.end_date ? 'date-filter' : ''">
              <p-calendar
                #startCal1
                appendTo="body"
                (onClose)="showCal1 ? endDateFilterSelected() : null"
                [(ngModel)]="dataFilter.end_date"
                showButtonBar="true"
                (onClearClick)="clearEndDate()"
                (onInput)="filterDate()"
                (onSelect)="onSelectDate(endDateKey)"
                (onTodayClick)="onTodayClicked(endDateKey)"
                inputStyleClass="p-0"
                class="position-absolute opacity-0 d-flex"
              >
                <ng-template pTemplate="header">
                  <span class="cal-drop">
                    <p-dropdown [(ngModel)]="dataFilter.selectedFilterEndDate" [options]="dateFilter"></p-dropdown>
                  </span>
                </ng-template>
              </p-calendar>
              <input
                pInputText
                type="text"
                [ngModel]="getFormattedFilterDate(endDateKey)"
                (click)="startCal1.toggle()"
                class="p-column-filter position-absolute h-100 w-100 pr-2"
              />
            </span>
            <span *ngIf="dataFilter.end_date" class="p-inputgroup-cancel"><em class="pi pi-times" (click)="onClearDateFilter(endDateKey)"></em></span>
          </div>
        </th>
        <th id="" *ngIf="_pCols.includes('duration')" class="duration-width"></th>
        <th id="searchBillType" *ngIf="_pCols.includes('bill_type')" class="billing-type-wrapper search-status-wrapper filter-header-status">
          <p-multiSelect
            [ngStyle]="{ color: 'black', fontFamily: 'Poppins', width: '100%' }"
            #billingTypeSelect
            appendTo="body"
            display="chip"
            [options]="billingType"
            placeholder="Billing Type"
            [(ngModel)]="selectedBillingType"
            (onChange)="billingTypeSelected($event)"
          >
          </p-multiSelect>
        </th>
        <th id="searchStatus" class="search-status-wrapper filter-header-status" *ngIf="_pCols.includes('status')">
          <p-multiSelect
            [ngStyle]="{ color: 'black', fontFamily: 'Poppins', width: '100%' }"
            appendTo="body"
            #multiSelectComp
            [options]="statuses"
            [(ngModel)]="selectedStatus"
            placeholder="Status"
            display="chip"
            (onChange)="statusSelected($event)"
          >
          </p-multiSelect>
        </th>
        <th *ngIf="_pCols.includes('description')"></th>
        <th id="tagging" *ngIf="_pCols.includes('tags')" class="tags-width">
          <p-treeSelect
            class="search-tag-wrapper"
            [(ngModel)]="selectedTags"
            (ngModelChange)="tagSelected($event)"
            [options]="groupedCategory?.data"
            display="chip"
            [metaKeySelection]="false"
            selectionMode="checkbox"
            placeholder="Select Tags"
            [ngModelOptions]="{ standalone: true }"
            [scrollHeight]="'400px'"
            filterBy="label"
            [appendTo]="'body'"
          >
          </p-treeSelect>
        </th>
        <ng-container *ngFor="let item of extendFields; let index = index">
          <!-- <p>{{ item.id }}</p> -->
          <ng-container *ngFor="let filed of item?.jsonData?.extendedFieldsConfig; let index2 = index">
            <ng-container *ngIf="filed?.component == componentType.Project || filed?.component == componentType.Client">
              <ng-container *ngFor="let filedDetails of filed?.fields; let index3 = index">
                <th colspan="1" *ngIf="checkSelectedColumn(filed?.component, filedDetails?.name) && filed?.component == componentType.Project" [id]="filedDetails?.name">
                  <span class="p-input-icon-right" *ngIf="filedDetails.DBTag">
                    <em class="pi pi-times" *ngIf="getFilterValue(filedDetails.DBTag)" (click)="deleteExtendFiledFilter(filedDetails?.DBTag)"></em>
                    <input
                      pInputText
                      type="text"
                      [value]="getFilterValue(filedDetails?.DBTag)"
                      (input)="filterExtendFiled(filedDetails?.DBTag, $event?.target)"
                      class="p-column-filter"
                      [placeholder]="filedDetails?.name"
                    />
                  </span>
                </th>
                <th colspan="1" *ngIf="checkSelectedColumn(filed?.component, filedDetails?.name) && filed?.component == componentType.Client" [id]="filedDetails?.name"></th>

                <!-- <input
              type="text"
              class="form-control custom mb-2"
              required
              [value]="getValueByPartialKey(filedDetails?.name)"
              (change)="
                updateObjectByPartialKey(
                  filedDetails?.name,
                  $event.target.value
                )
              "
              (focus)="
                updateObjectByPartialKey(
                  filedDetails?.name,
                  $event.target.value
                )
              "
              [placeholder]="filedDetails?.name"
            /> -->
              </ng-container>
            </ng-container>
          </ng-container>
        </ng-container>
        <th class="action-width d-flex justify-content-center"></th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-project>
      <tr>
        <td class="d-flex justify-content-center" style="max-width: 42px !important; height: 40px">
          <p-tableCheckbox [value]="project"></p-tableCheckbox>
        </td>
        <td *ngIf="_pCols.includes('name')" class="customer-width">
          <a [routerLink]="[appRoutes.EDIT_CLIENT, project?.project?.customer?.id]" *hasAnyPermission="permissionModules.MANAGE_CLIENT; disableEvent: true">
            <span [title]="project?.project?.customer?.name">
              {{ project?.project?.customer?.name }}
            </span>
          </a>
        </td>
        <td *ngIf="_pCols.includes('project_name')" class="project-width">
          <a class="cursor-pointer" (click)="onProjectClick(project)" *hasAnyPermission="[permissionModules.MANAGE_PROJECT, permissionModules.VIEW_PROJECT]; disableEvent: true">
            <span [title]="project?.project?.name">
              {{ project?.project?.name }}
            </span>
          </a>
        </td>
        <td class="text-number-right start-end-width" *ngIf="_pCols.includes('start_date')">
          {{ project?.project?.start_date | date : 'MM/dd/yyyy' }}
        </td>
        <td class="text-number-right start-end-width" *ngIf="_pCols.includes('end_date')">{{ project?.project?.end_date | date : 'MM/dd/yyyy' }}<br /></td>
        <td class="text-number-right duration-width" *ngIf="_pCols.includes('duration')">
          {{ project?.project?.start_date && project?.project?.end_date ? getNoOfWeeks(project?.project) : '' }}
        </td>

        <td *ngIf="_pCols.includes('bill_type')" class="filter-header-status">
          {{ project?.project?.billing_type }}
        </td>
        <td *ngIf="_pCols.includes('status')" class="filter-header-status">
          <span>{{ project?.project?.status }}</span>
        </td>
        <td *ngIf="_pCols.includes('description')">
          <ng-container *ngIf="project?.project?.description">
            <span class="text-truncate">{{ project?.project?.description }} </span>
            <fa-icon class="ml-1 pointe informative-Icon" icon="info-circle" (click)="updateDescriptionPopUPObj(project?.project?.name, project?.project?.description)"></fa-icon>
          </ng-container>
        </td>
        <td class="header-width tags-width tags-td" *ngIf="_pCols.includes('tags')">
          <ng-container *ngIf="project?.project?.tags?.length">
            <span class="ellipses">
              <span class="taglist">
                <p-chip *ngFor="let tag of project?.project?.tags; let i = index" (click)="openTagModal(project?.project?.tags)" class="cursor-pointer">
                  <span class="tooltip-hover" [ngbTooltip]="categoryDetails" #t2="ngbTooltip" (mouseenter)="toggleWithCategory(t2, tag)">{{ getTagsCount(tag, i < 2) }}</span>
                </p-chip>
              </span>
            </span>
            <span class="count cursor-pointer" *ngIf="project?.project?.tags?.length > 2" (click)="openTagModal(project?.project?.tags)">
              <span class="tag-count">
                <p-badge [value]="getTagCount(project?.project?.tags)"></p-badge>
              </span>
            </span>
          </ng-container>
        </td>
        <ng-container *ngFor="let item of extendFields; let index = index">
          <!-- <p>{{ item.id }}</p> -->
          <ng-container *ngFor="let filed of item?.jsonData?.extendedFieldsConfig; let index2 = index">
            <ng-container *ngIf="filed?.component == componentType.Project || filed?.component == componentType.Client">
              <ng-container *ngFor="let filedDetails of filed?.fields; let index3 = index">
                <td
                  class="show-pointer"
                  colspan="1"
                  *ngIf="checkSelectedColumn(filed?.component, filedDetails?.name) && filed?.component == componentType.Client"
                  [id]="filedDetails?.name"
                  [ngClass]="{ 'dynamic-text text-truncate': filedDetails.type === filedType.Text_Area }"
                >
                  {{ getValueBYExtendFiled(filed?.component, project, filedDetails?.DBTag) }}
                </td>
                <td
                  class="show-pointer"
                  colspan="1"
                  *ngIf="checkSelectedColumn(filed?.component, filedDetails?.name) && filed?.component == componentType.Project"
                  [id]="filedDetails?.name"
                  (click)="openExtendFiledPopup(project, filedDetails?.name)"
                  [ngClass]="{ 'dynamic-text text-truncate': filedDetails.type === filedType.Text_Area }"
                >
                  {{ getValueBYExtendFiled(filed?.component, project, filedDetails?.DBTag) }}
                </td>
                <!-- <input
              type="text"
              class="form-control custom mb-2"
              required
              [value]="getValueByPartialKey(filedDetails?.name)"
              (change)="
                updateObjectByPartialKey(
                  filedDetails?.name,
                  $event.target.value
                )
              "
              (focus)="
                updateObjectByPartialKey(
                  filedDetails?.name,
                  $event.target.value
                )
              "
              [placeholder]="filedDetails?.name"
            /> -->
              </ng-container>
            </ng-container>
          </ng-container>
        </ng-container>
        <td class="text-center action-width justify-content-center" colspan="3">
          <a
            title="Edit"
            class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary cursor-pointer"
            (click)="onProjectClick(project)"
            *hasAnyPermission="[permissionModules.MANAGE_PROJECT, permissionModules.VIEW_PROJECT]"
          >
            <span [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
          </a>
          <a
            title="Duplicate"
            class="btn btn-icon btn-light btn-sm btn-hover-primary cursor-pointer icon-background-white"
            *hasAnyPermission="[permissionModules?.MANAGE_PROJECT]; hideTemplate: true"
            (click)="copyProject(project?.project?.id)"
          >
            <em class="pi pi-copy svg-icon svg-icon-md"></em>
          </a>
          <a
            [title]="project?.project?.status !== 'Draft' ? 'You can only delete projects that are in draft status' : 'Archive'"
            class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary"
            (click)="confirmDeleteProject(project?.project?.id, project?.project?.status === 'Draft')"
            [ngClass]="{
              'button-disabled': project?.project?.status !== 'Draft'
            }"
          >
            <span
              [inlineSVG]="'assets/media/svg/icons/archive.svg'"
              cacheSVG="true"
              class="svg-icon svg-icon-md"
              *hasAnyPermission="permissionModules.MANAGE_PROJECT; disableEvent: true"
            >
            </span>
          </a>
        </td>
      </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
      <tr>
        <td colspan="12" class="center-align">No Projects found.</td>
      </tr>
    </ng-template>
  </p-table>
</div>

<ng-template #categoryDetails let-tag="tag">
  <p [innerHTML]="getTagCategorySubCategory(tag)"></p>
</ng-template>
<div class="dialog-applied-tags">
  <p-dialog header="Applied Tags" [(visible)]="showTagDialog" [modal]="true" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
    <ul>
      <li *ngFor="let tag of selectedTagToView">
        <span [ngbTooltip]="categoryDetails" #t3="ngbTooltip" (mouseenter)="toggleWithCategory(t3, tag)">{{ getExtractedTags(tag) }}</span>
      </li>
    </ul>
  </p-dialog>
</div>

<p-dialog header="Delete Project" [(visible)]="showDeleteDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0">Are you sure you want to delete this project?</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="deleteProject()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog header="Rename" [(visible)]="showEditDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <div class="form-group first" *ngIf="editFilterObj">
    <input type="text" pInputText class="form-control custom" [(ngModel)]="editFilterObj.query_filter.name" (input)="inputFilterName()" />
    <small *ngIf="showNameError" class="form-text text-danger"> Name is required</small>
  </div>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">Cancel</button>
      <button type="button" class="btn-save" (click)="saveEditFilter()" [isSubmitting]="isSubmitting">Update</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog header="Delete" [(visible)]="showDeleteFilterDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0 delete-title">Do you want to delete "{{ deleteFilterObj?.query_filter?.name }}" Filter?</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="saveDeleteFilter()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog [header]="shareFilterObj?.header" [(visible)]="showShareDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0 delete-title">{{ shareFilterObj?.text }}</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="saveShareFilter()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog
  dismissableMask="true"
  backdrop="false"
  position="top-right"
  [(visible)]="showSavedFilter"
  [modal]="true"
  class="filter-dialog-manage-project"
  [draggable]="false"
  [resizable]="false"
>
  <ng-template pTemplate>
    <div class="filter-listing">
      <input type="search" class="form-control mb-2" placeholder="Search" (input)="searchFilters($event.target.value)" />
      <div *hasAnyPermission="permissionModules.VIEW_SHARE_FILTER; hideTemplate: true" class="title">Shared Filters</div>
      <ng-container *hasAnyPermission="permissionModules.VIEW_SHARE_FILTER; hideTemplate: true">
        <span *ngIf="sharedFilters?.length; else noData">
          <div
            class="form-check filter-body"
            *ngFor="let filterOption of sharedFilters"
            [ngClass]="{
              'selected-filter': filterOption?.query_filter?.id === selectedFilterFormControl?.value?.query_filter?.id
            }"
          >
            <label class="form-check-label">
              <input
                [formControl]="selectedFilterFormControl"
                (ngModelChange)="applySelectedFilterAndUpdateUrl()"
                type="radio"
                class="form-check-input custom-radio"
                [value]="filterOption"
                name="filteroption"
              />{{ filterOption?.query_filter?.name }}
            </label>
            <div class="filter-icons">
              <ng-container *hasAnyPermission="permissionModules.MANAGE_SHARE_FILTER; hideTemplate: true">
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary copy-icon" (click)="copyLinkToTheFilter(filterOption?.query_filter?.id)">
                  <em title="Copy Filter Link" class="fa-regular fa-copy"></em>
                </a>
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary share-icon" (click)="unShareFilter(filterOption)">
                  <em class="fa-solid fa-share" title="UnShare Filter"></em>
                </a>
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
                  <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="editFilter(filterOption)"> </span>
                </a>
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
                  <span title="Delete" [inlineSVG]="'assets/media/svg/icons/delete.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="deleteFilter(filterOption)"> </span>
                </a>
              </ng-container>
            </div>
          </div>
        </span>
      </ng-container>

      <ng-template #noData>
        <div>-</div>
      </ng-template>
      <div class="title">My Filters</div>
      <span *ngIf="myFilters?.length; else noData">
        <div
          class="form-check filter-body"
          *ngFor="let filterOption of myFilters"
          [ngClass]="{
            'selected-filter': filterOption?.query_filter?.id === selectedFilterFormControl?.value?.query_filter?.id
          }"
        >
          <label class="form-check-label">
            <input
              [formControl]="selectedFilterFormControl"
              (ngModelChange)="applySelectedFilterAndUpdateUrl()"
              type="radio"
              class="form-check-input custom-radio"
              [value]="filterOption"
              name="filteroption"
            />{{ filterOption?.query_filter?.name }}
          </label>
          <div class="filter-icons">
            <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary copy-icon" (click)="copyLinkToTheFilter(filterOption?.query_filter?.id)">
              <em title="Copy Filter Link" class="fa-regular fa-copy"></em>
            </a>
            <a
              class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary"
              (click)="shareFilter(filterOption)"
              *hasAnyPermission="permissionModules.MANAGE_SHARE_FILTER; hideTemplate: true"
            >
              <em class="fa-solid fa-share" title="Share Filter"></em>
            </a>
            <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
              <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="editFilter(filterOption)"> </span>
            </a>
            <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
              <span title="Delete" [inlineSVG]="'assets/media/svg/icons/delete.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="deleteFilter(filterOption)"> </span>
            </a>
          </div>
        </div>
      </span>
    </div>
  </ng-template>
</p-dialog>

<p-dialog
  [header]="descriptionGlobalName"
  [(visible)]="showdescriptionDialog"
  [modal]="true"
  class="description-dialog"
  [baseZIndex]="10000"
  [draggable]="false"
  [resizable]="false"
  [style]="{ width: '40vw' }"
>
  <h3>
    <label>Description :</label>
  </h3>
  <div class="d-flex align-content-center mb-2">
    <textarea class="text text-secondary description-text" name="description" id="description" [rows]="descriptionRows" [disabled]="true" [ngModel]="descriptionGlobal"> </textarea>
  </div>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <!-- <button type="button" class="btn-cancel" (click)="closeModal()">
        No
      </button> -->
      <button type="button" class="btn-save" (click)="showdescriptionDialog = false" [isSubmitting]="isSubmitting">Ok</button>
    </div>
  </ng-template>
</p-dialog>

<ng-template #extendForm>
  <div class="row col-12 py-5">
    <ng-container *ngFor="let item of extendFields; let index = index">
      <!-- <p>{{ item.id }}</p> -->
      <ng-container *ngFor="let filed of item?.jsonData?.extendedFieldsConfig; let index2 = index">
        <ng-container *ngIf="filed?.component == componentType.Project || filed?.component == componentType.Client">
          <ng-container *ngFor="let filedDetails of filed?.fields; let index3 = index">
            <label class="form-label" [for]="filedDetails?.name">{{ filedDetails?.name }}</label>
            <input
              type="text"
              class="form-control custom mb-2"
              required
              [value]="getValueByPartialKey(filedDetails?.name)"
              (change)="updateObjectByPartialKey(filedDetails?.name, $event.target.value)"
              (focus)="updateObjectByPartialKey(filedDetails?.name, $event.target.value)"
              [placeholder]="filedDetails?.name"
            />
          </ng-container>
        </ng-container>
      </ng-container>
    </ng-container>
  </div>
</ng-template>

<p-dialog
  [header]="'Update ' + updateExtendFiled"
  [(visible)]="showUpdateExtendFiledDialog"
  [modal]="true"
  class="description-dialog"
  [baseZIndex]="10000"
  [draggable]="false"
  [resizable]="false"
  [style]="{ width: '40vw' }"
>
  <app-extended-form [extendFieldsObj]="projectObj?.project?.extended_fields" [filedName]="updateExtendFiled" [componentType]="componentType.Project"> </app-extended-form>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeExtendFiledPopup()">Cancel</button>
      <button type="button" class="btn-save" (click)="saveProject()" [isSubmitting]="isSubmitting">Save</button>
    </div>
  </ng-template>
</p-dialog>
